"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Trophy, X, Check } from "lucide-react"
import { toast } from "sonner"

interface CriarBolaoModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
  editData?: any // Dados do bolão para edição
  isEditing?: boolean // Flag para indicar se está editando
}

interface Campeonato {
  id: number
  nome: string
  descricao: string
  status: string
  pais: string
  logo_url?: string
  total_jogos?: number
  jogos_futuros?: number
}

interface Partida {
  id: number
  time_casa_nome: string
  time_fora_nome: string
  time_casa_curto: string
  time_fora_curto: string
  time_casa_logo?: string
  time_fora_logo?: string
  data_jogo: string
  campeonato_nome: string
  status: string
}

// Campeonatos serão carregados da API

// Partidas serão carregadas da API

export function CriarBolaoModal({ open, onOpenChange, onSuccess, editData, isEditing = false }: CriarBolaoModalProps) {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [campeonatos, setCampeonatos] = useState<Campeonato[]>([])
  const [loadingCampeonatos, setLoadingCampeonatos] = useState(false)
  const [partidas, setPartidas] = useState<Partida[]>([])
  const [loadingPartidas, setLoadingPartidas] = useState(false)
  // Função para obter data/hora atual no formato datetime-local
  const getCurrentDateTime = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day}T${hours}:${minutes}`
  }

  // Função para obter data/hora + 7 dias no formato datetime-local
  const getDateTimePlus7Days = () => {
    const now = new Date()
    now.setDate(now.getDate() + 7) // Adiciona 7 dias
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day}T${hours}:${minutes}`
  }

  const [formData, setFormData] = useState({
    nome: "",
    valorBolao: "25",
    premioTotal: "500",
    maxParticipantes: "100",
    dataInicio: getCurrentDateTime(),
    dataFim: getDateTimePlus7Days()
  })

  // Preencher dados quando estiver editando
  useEffect(() => {
    if (isEditing && editData && open) {
      // Função para converter data do banco para formato datetime-local
      const formatDateTimeForInput = (dateString: string) => {
        if (!dateString) return getCurrentDateTime()
        const date = new Date(dateString)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        return `${year}-${month}-${day}T${hours}:${minutes}`
      }

      setFormData({
        nome: editData.nome || "",
        valorBolao: editData.valor_aposta?.toString() || "25",
        premioTotal: editData.premio_total?.toString() || "500",
        maxParticipantes: editData.max_participantes?.toString() || "100",
        dataInicio: formatDateTimeForInput(editData.data_inicio),
        dataFim: formatDateTimeForInput(editData.data_fim)
      })

      // Preencher campeonatos selecionados
      try {
        const campeonatosData = typeof editData.campeonatos_selecionados === 'string'
          ? JSON.parse(editData.campeonatos_selecionados)
          : editData.campeonatos_selecionados || []
        setCampeonatosSelecionados(campeonatosData.map((c: any) => c.id || c))
      } catch (e) {
        console.error('Erro ao parsear campeonatos:', e)
        setCampeonatosSelecionados([])
      }

      // Preencher partidas selecionadas
      try {
        const partidasData = typeof editData.partidas_selecionadas === 'string'
          ? JSON.parse(editData.partidas_selecionadas)
          : editData.partidas_selecionadas || []
        setPartidasSelecionadas(partidasData.map((p: any) => p.id || p))
      } catch (e) {
        console.error('Erro ao parsear partidas:', e)
        setPartidasSelecionadas([])
      }
    }
  }, [isEditing, editData, open])

  // Reset form quando modal for fechado
  const handleModalClose = (open: boolean) => {
    if (!open) {
      // Reset form apenas se não estiver carregando
      if (!loading) {
        setStep(1)
        setFormData({
          nome: "",
          valorBolao: "25",
          premioTotal: "500",
          maxParticipantes: "100",
          dataInicio: getCurrentDateTime(),
          dataFim: getDateTimePlus7Days()
        })
        setCampeonatosSelecionados([])
        setPartidasSelecionadas([])
      }
    }
    onOpenChange(open)
  }
  const [campeonatosSelecionados, setCampeonatosSelecionados] = useState<number[]>([])
  const [partidasSelecionadas, setPartidasSelecionadas] = useState<number[]>([])

  // Função para carregar campeonatos da API
  const fetchCampeonatos = async () => {
    try {
      setLoadingCampeonatos(true)
      const response = await fetch('/api/campeonatos?status=ativo&limit=100')
      const data = await response.json()

      if (data.success && data.campeonatos) {
        // Mapear dados da API para o formato esperado
        const campeonatosFormatados = data.campeonatos.map((camp: any) => ({
          id: camp.id,
          nome: camp.nome,
          descricao: camp.descricao || camp.nome,
          status: camp.status === 'ativo' ? 'Em Andamento' : 'Finalizado',
          pais: camp.pais || 'Internacional',
          logo_url: camp.logo_url,
          total_jogos: camp.total_jogos || 0,
          jogos_futuros: camp.jogos_futuros || 0
        }))
        setCampeonatos(campeonatosFormatados)
      }
    } catch (error) {
      console.error('Erro ao carregar campeonatos:', error)
      toast.error('Erro ao carregar campeonatos')
    } finally {
      setLoadingCampeonatos(false)
    }
  }

  // Função para carregar partidas da API baseado nos campeonatos selecionados
  const fetchPartidas = async (campeonatosIds: number[] = []) => {
    try {
      setLoadingPartidas(true)

      // Se não há campeonatos selecionados, limpar partidas
      if (campeonatosIds.length === 0) {
        setPartidas([])
        return
      }

      console.log(`🔍 Buscando partidas para campeonatos: ${campeonatosIds.join(', ')}`)

      // Buscar partidas para cada campeonato selecionado
      const todasPartidas = []

      for (const campeonatoId of campeonatosIds) {
        try {
          const response = await fetch(`/api/jogos?status=agendado&campeonato=${campeonatoId}&limit=1000`)
          const data = await response.json()

          if (data.success && data.jogos) {
            console.log(`📊 Campeonato ${campeonatoId}: ${data.jogos.length} partidas encontradas`)
            todasPartidas.push(...data.jogos)
          } else {
            console.warn(`⚠️ Nenhuma partida encontrada para campeonato ${campeonatoId}`)
          }
        } catch (error) {
          console.error(`❌ Erro ao buscar partidas do campeonato ${campeonatoId}:`, error)
        }
      }

      console.log(`✅ Total de partidas carregadas: ${todasPartidas.length}`)
      setPartidas(todasPartidas)

    } catch (error) {
      console.error('Erro ao carregar partidas:', error)
      toast.error('Erro ao carregar partidas')
    } finally {
      setLoadingPartidas(false)
    }
  }

  // Carregar campeonatos quando o modal abrir
  useEffect(() => {
    if (open) {
      fetchCampeonatos()
    }
  }, [open])

  // Carregar partidas quando campeonatos forem selecionados
  useEffect(() => {
    if (campeonatos.length > 0) {
      fetchPartidas(campeonatosSelecionados)
    }
  }, [campeonatosSelecionados, campeonatos])

  const handleNextStep = () => {
    if (step === 1) {
      if (!formData.nome || !formData.valorBolao || !formData.premioTotal || !formData.dataInicio || !formData.dataFim) {
        toast.error("Preencha todos os campos obrigatórios")
        return
      }
    }
    if (step === 2 && campeonatosSelecionados.length === 0) {
      toast.error("Selecione pelo menos um campeonato")
      return
    }
    if (step === 3 && partidasSelecionadas.length === 0) {
      toast.error("Selecione pelo menos uma partida")
      return
    }
    setStep(step + 1)
  }

  const handlePrevStep = () => {
    setStep(step - 1)
  }

  const handleCampeonatoToggle = (id: number) => {
    setCampeonatosSelecionados((prev) => (prev.includes(id) ? prev.filter((c) => c !== id) : [...prev, id]))
  }

  const handlePartidaToggle = (id: number) => {
    if (partidasSelecionadas.length >= 11 && !partidasSelecionadas.includes(id)) {
      toast.error("Máximo de 11 partidas permitidas")
      return
    }
    setPartidasSelecionadas((prev) => (prev.includes(id) ? prev.filter((p) => p !== id) : [...prev, id]))
  }

  const handleFinish = async () => {
    try {
      setLoading(true)
      console.log(isEditing ? "🚀 Iniciando edição de bolão..." : "🚀 Iniciando criação de bolão...")

      const bolaoData = {
        nome: formData.nome,
        descricao: "",
        valor_aposta: parseFloat(formData.valorBolao),
        premio_total: parseFloat(formData.premioTotal),
        max_participantes: parseInt(formData.maxParticipantes),
        min_acertos: 3,
        data_inicio: formData.dataInicio,
        data_fim: formData.dataFim,
        status: isEditing ? editData?.status || "ativo" : "ativo",
        campeonatos_selecionados: campeonatosSelecionados.map((id) => campeonatos.find((c) => c.id === id)),
        partidas_selecionadas: partidasSelecionadas.map((id) => partidas.find((p) => p.id === id))
      }

      console.log("📝 Dados do bolão:", bolaoData)

      const url = isEditing ? `/api/admin/boloes/${editData.id}` : "/api/admin/boloes"
      const method = isEditing ? "PUT" : "POST"

      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(bolaoData),
      })

      console.log("📡 Response status:", response.status)
      const result = await response.json()
      console.log("📋 Response data:", result)

      if (result.success) {
        toast.success(isEditing ? "Bolão atualizado com sucesso!" : "Bolão criado com sucesso!")

        // Reset form
        setStep(1)
        setFormData({
          nome: "",
          valorBolao: "25",
          premioTotal: "500",
          maxParticipantes: "100",
          dataInicio: getCurrentDateTime(),
          dataFim: getDateTimePlus7Days()
        })
        setCampeonatosSelecionados([])
        setPartidasSelecionadas([])

        handleModalClose(false)

        // Chamar callback de sucesso se fornecido
        if (onSuccess) {
          onSuccess()
        }
      } else {
        console.error("❌ Erro na resposta:", result)
        toast.error(result.error || (isEditing ? "Erro ao atualizar bolão" : "Erro ao criar bolão"))
      }
    } catch (error) {
      console.error(isEditing ? "❌ Erro ao atualizar bolão:" : "❌ Erro ao criar bolão:", error)
      toast.error(isEditing ? "Erro ao atualizar bolão" : "Erro ao criar bolão")
    } finally {
      setLoading(false)
    }
  }

  // As partidas já estão filtradas na função fetchPartidas
  const partidasFiltradas = partidas

  return (
    <Dialog open={open} onOpenChange={handleModalClose}>
      <DialogContent className="w-[95vw] max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {step === 1 && (isEditing ? "Editar Bolão - Dados Básicos" : "Criar Novo Bolão - Dados Básicos")}
            {step === 2 && "Selecionar Campeonatos"}
            {step === 3 && "Selecionar Partidas"}
          </DialogTitle>
          <DialogDescription>
            {step === 1 && "Configure os dados básicos do bolão"}
            {step === 2 && "Escolha os campeonatos que farão parte do bolão"}
            {step === 3 && "Selecione as partidas específicas para o bolão"}
          </DialogDescription>
        </DialogHeader>

        {/* Step 1: Dados Básicos */}
        {step === 1 && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="nome">Nome do Bolão *</Label>
                <Input
                  id="nome"
                  value={formData.nome}
                  onChange={(e) => setFormData({...formData, nome: e.target.value})}
                  placeholder="Ex: Brasileirão 2024"
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="valorBolao">Valor da Aposta (R$) *</Label>
                <Input
                  id="valorBolao"
                  type="number"
                  value={formData.valorBolao}
                  onChange={(e) => setFormData({...formData, valorBolao: e.target.value})}
                  placeholder="25.00"
                  className="mt-2"
                  step="0.01"
                  min="1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="premioTotal">Prêmio Total (R$) *</Label>
                <Input
                  id="premioTotal"
                  type="number"
                  value={formData.premioTotal}
                  onChange={(e) => setFormData({...formData, premioTotal: e.target.value})}
                  placeholder="500.00"
                  className="mt-2"
                  step="0.01"
                  min="1"
                />
              </div>
              <div>
                <Label htmlFor="maxParticipantes">Máx. Participantes</Label>
                <Input
                  id="maxParticipantes"
                  type="number"
                  value={formData.maxParticipantes}
                  onChange={(e) => setFormData({...formData, maxParticipantes: e.target.value})}
                  placeholder="100"
                  className="mt-2"
                  min="1"
                />
              </div>
            </div>



            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="dataInicio">Data de Início *</Label>
                <Input
                  id="dataInicio"
                  type="datetime-local"
                  value={formData.dataInicio}
                  onChange={(e) => setFormData({...formData, dataInicio: e.target.value})}
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="dataFim">Data de Fim *</Label>
                <Input
                  id="dataFim"
                  type="datetime-local"
                  value={formData.dataFim}
                  onChange={(e) => setFormData({...formData, dataFim: e.target.value})}
                  className="mt-2"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => handleModalClose(false)}>
                Cancelar
              </Button>
              <Button onClick={handleNextStep}>Próximo: Selecionar Campeonatos</Button>
            </div>
          </div>
        )}

        {/* Step 2: Selecionar Campeonatos */}
        {step === 2 && (
          <div className="space-y-6">
            <p className="text-gray-600">
              Escolha os campeonatos para este bolão ({campeonatosSelecionados.length} selecionados):
            </p>

            {loadingCampeonatos ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
                <span className="ml-2 text-gray-600">Carregando campeonatos...</span>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {campeonatos.map((campeonato) => (
                  <Card
                    key={campeonato.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      campeonatosSelecionados.includes(campeonato.id)
                        ? "ring-2 ring-green-500 bg-green-50"
                        : "hover:bg-gray-50"
                    }`}
                    onClick={() => handleCampeonatoToggle(campeonato.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center">
                            {campeonato.logo_url ? (
                              <img
                                src={campeonato.logo_url}
                                alt={campeonato.nome}
                                className="w-8 h-8 sm:w-10 sm:h-10 object-contain"
                                onError={(e) => {
                                  const target = e.currentTarget as HTMLImageElement
                                  target.style.display = 'none'
                                  const fallback = target.nextElementSibling as HTMLElement
                                  if (fallback) fallback.style.display = 'flex'
                                }}
                              />
                            ) : null}
                            <div
                              className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center text-white text-lg sm:text-xl shadow-md"
                              style={{ display: campeonato.logo_url ? 'none' : 'flex' }}
                            >
                              🏆
                            </div>
                          </div>
                          <div>
                            <h3 className="font-semibold">{campeonato.nome}</h3>
                            <p className="text-sm text-gray-600">{campeonato.descricao}</p>
                            {campeonato.total_jogos > 0 && (
                              <p className="text-xs text-blue-600">
                                {campeonato.jogos_futuros} jogos futuros
                              </p>
                            )}
                          </div>
                        </div>
                        {campeonatosSelecionados.includes(campeonato.id) && <Check className="h-5 w-5 text-green-600" />}
                      </div>
                      <div className="mt-2 flex items-center justify-between">
                        <Badge variant={campeonato.status === "Em Andamento" ? "default" : "secondary"}>
                          {campeonato.status}
                        </Badge>
                        <span className="text-xs text-gray-500">{campeonato.pais}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            <div className="flex justify-between">
              <Button variant="outline" onClick={handlePrevStep}>
                Voltar
              </Button>
              <div className="space-x-2">
                <Button variant="outline" onClick={() => handleModalClose(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleNextStep} disabled={campeonatosSelecionados.length === 0}>
                  Próximo: Ver Partidas ({campeonatosSelecionados.length} campeonatos)
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Selecionar Partidas */}
        {step === 3 && (
          <div className="space-y-6">
            <div>
              <p className="text-gray-600">
                Selecione até 11 partidas para este bolão ({partidasSelecionadas.length}/11 selecionadas):
              </p>

              {/* Campeonatos Selecionados */}
              <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Trophy className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium text-yellow-800">
                    {campeonatosSelecionados.length} Campeonatos Selecionados
                  </span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {campeonatosSelecionados.map((id) => {
                    const camp = campeonatos.find((c) => c.id === id)
                    return camp ? (
                      <Badge key={id} variant="outline" className="text-yellow-800 border-yellow-300">
                        {camp.nome}
                      </Badge>
                    ) : null
                  })}
                </div>
              </div>

              {/* Partidas Selecionadas */}
              {partidasSelecionadas.length > 0 && (
                <div className="mt-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Check className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">
                      Partidas Selecionadas ({partidasSelecionadas.length})
                    </span>
                  </div>
                  <div className="space-y-2">
                    {partidasSelecionadas.map((id) => {
                      const partida = partidas.find((p) => p.id === id)
                      return partida ? (
                        <div
                          key={id}
                          className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded"
                        >
                          <div className="flex items-center space-x-2 sm:space-x-3">
                            {/* Time Casa */}
                            <div className="flex items-center space-x-1 sm:space-x-2">
                              <div className="w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center">
                                {partida.time_casa_logo ? (
                                  <img
                                    src={partida.time_casa_logo}
                                    alt={partida.time_casa_nome}
                                    className="w-5 h-5 sm:w-6 sm:h-6 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-5 h-5 sm:w-6 sm:h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-bold text-blue-700"
                                  style={{ display: partida.time_casa_logo ? 'none' : 'flex' }}
                                >
                                  {partida.time_casa_nome.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                              <span className="font-medium text-xs sm:text-sm">{partida.time_casa_curto || partida.time_casa_nome}</span>
                            </div>

                            <span className="text-xs sm:text-sm font-bold text-gray-400">VS</span>

                            {/* Time Fora */}
                            <div className="flex items-center space-x-1 sm:space-x-2">
                              <span className="font-medium text-xs sm:text-sm">{partida.time_fora_curto || partida.time_fora_nome}</span>
                              <div className="w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center">
                                {partida.time_fora_logo ? (
                                  <img
                                    src={partida.time_fora_logo}
                                    alt={partida.time_fora_nome}
                                    className="w-5 h-5 sm:w-6 sm:h-6 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-5 h-5 sm:w-6 sm:h-6 bg-red-100 rounded-full flex items-center justify-center text-xs font-bold text-red-700"
                                  style={{ display: partida.time_fora_logo ? 'none' : 'flex' }}
                                >
                                  {partida.time_fora_nome.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                            </div>

                            <div className="text-xs sm:text-sm text-gray-600">
                              {new Date(partida.data_jogo).toLocaleDateString('pt-BR')} - {new Date(partida.data_jogo).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" onClick={() => handlePartidaToggle(id)}>
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : null
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Partidas Disponíveis */}
            <div>
              <h4 className="font-medium mb-3">Partidas Disponíveis ({partidasFiltradas.length})</h4>
              {loadingPartidas ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                  <span className="ml-2 text-gray-600">Carregando partidas...</span>
                </div>
              ) : (
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {partidasFiltradas.map((partida) => {
                    const dataJogo = new Date(partida.data_jogo)
                    const dataFormatada = dataJogo.toLocaleDateString('pt-BR')
                    const horaFormatada = dataJogo.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })

                    return (
                      <Card
                        key={partida.id}
                        className={`cursor-pointer transition-all hover:shadow-sm ${
                          partidasSelecionadas.includes(partida.id)
                            ? "ring-2 ring-green-500 bg-green-50"
                            : "hover:bg-gray-50"
                        }`}
                        onClick={() => handlePartidaToggle(partida.id)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 sm:space-x-4">
                              {/* Time Casa */}
                              <div className="flex items-center space-x-1 sm:space-x-2">
                                <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center">
                                  {partida.time_casa_logo ? (
                                    <img
                                      src={partida.time_casa_logo}
                                      alt={partida.time_casa_nome}
                                      className="w-6 h-6 sm:w-8 sm:h-8 object-contain"
                                      onError={(e) => {
                                        const target = e.currentTarget as HTMLImageElement
                                        target.style.display = 'none'
                                        const fallback = target.nextElementSibling as HTMLElement
                                        if (fallback) fallback.style.display = 'flex'
                                      }}
                                    />
                                  ) : null}
                                  <div
                                    className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center text-xs font-bold text-blue-700"
                                    style={{ display: partida.time_casa_logo ? 'none' : 'flex' }}
                                  >
                                    {partida.time_casa_nome.substring(0, 2).toUpperCase()}
                                  </div>
                                </div>
                                <div className="text-center">
                                  <div className="font-medium text-xs sm:text-sm">{partida.time_casa_curto || partida.time_casa_nome}</div>
                                </div>
                              </div>

                              <div className="text-sm sm:text-lg font-bold text-gray-400">VS</div>

                              {/* Time Fora */}
                              <div className="flex items-center space-x-1 sm:space-x-2">
                                <div className="text-center">
                                  <div className="font-medium text-xs sm:text-sm">{partida.time_fora_curto || partida.time_fora_nome}</div>
                                </div>
                                <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center">
                                  {partida.time_fora_logo ? (
                                    <img
                                      src={partida.time_fora_logo}
                                      alt={partida.time_fora_nome}
                                      className="w-6 h-6 sm:w-8 sm:h-8 object-contain"
                                      onError={(e) => {
                                        const target = e.currentTarget as HTMLImageElement
                                        target.style.display = 'none'
                                        const fallback = target.nextElementSibling as HTMLElement
                                        if (fallback) fallback.style.display = 'flex'
                                      }}
                                    />
                                  ) : null}
                                  <div
                                    className="w-6 h-6 sm:w-8 sm:h-8 bg-red-100 rounded-full flex items-center justify-center text-xs font-bold text-red-700"
                                    style={{ display: partida.time_fora_logo ? 'none' : 'flex' }}
                                  >
                                    {partida.time_fora_nome.substring(0, 2).toUpperCase()}
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="text-right">
                              <div className="text-xs sm:text-sm font-medium">
                                <span className="hidden sm:inline">{dataFormatada} - {horaFormatada}</span>
                                <span className="sm:hidden">{dataFormatada}</span>
                              </div>
                              <Badge variant="outline" className="text-xs">
                                {partida.status.toUpperCase()}
                              </Badge>
                            </div>
                          </div>
                          <div className="mt-2 text-xs text-gray-500">
                            <span className="hidden sm:inline">{partida.campeonato_nome}</span>
                            <span className="sm:hidden">{partida.campeonato_nome.length > 15 ? partida.campeonato_nome.substring(0, 15) + '...' : partida.campeonato_nome}</span>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              )}
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={handlePrevStep}>
                Voltar
              </Button>
              <div className="space-x-2">
                <Button variant="outline" onClick={() => handleModalClose(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleFinish} disabled={partidasSelecionadas.length === 0 || loading}>
                  {loading ? (isEditing ? "Atualizando..." : "Criando...") : (isEditing ? `Atualizar Bolão - R$ ${formData.valorBolao}` : `Criar Bolão - R$ ${formData.valorBolao}`)}
                </Button>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
