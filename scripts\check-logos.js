#!/usr/bin/env node

/**
 * Script para verificar logos dos times e campeonatos
 */

import { config } from 'dotenv'
import { initializeDatabase, executeQuery } from '../lib/database-config.js'

// Carregar variáveis de ambiente
config({ path: '.env.local' })

async function checkLogos() {
  try {
    console.log('🔍 Verificando logos...')
    
    await initializeDatabase()
    
    // Verificar campeonatos com/sem logos
    const campeonatos = await executeQuery(`
      SELECT nome, logo_url FROM campeonatos 
      WHERE status = 'ativo'
      ORDER BY nome
      LIMIT 10
    `)
    
    console.log('\n🏆 CAMPEONATOS:')
    campeonatos.forEach(camp => {
      const hasLogo = camp.logo_url ? '✅' : '❌'
      console.log(`${hasLogo} ${camp.nome} - ${camp.logo_url || 'sem logo'}`)
    })
    
    // Verificar times com/sem logos
    const times = await executeQuery(`
      SELECT nome, logo_url FROM times 
      ORDER BY nome
      LIMIT 10
    `)
    
    console.log('\n👥 TIMES:')
    times.forEach(time => {
      const hasLogo = time.logo_url ? '✅' : '❌'
      console.log(`${hasLogo} ${time.nome} - ${time.logo_url || 'sem logo'}`)
    })
    
    // Estatísticas
    const statsCampeonatos = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(logo_url) as com_logo,
        (COUNT(*) - COUNT(logo_url)) as sem_logo
      FROM campeonatos 
      WHERE status = 'ativo'
    `)
    
    const statsTimes = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(logo_url) as com_logo,
        (COUNT(*) - COUNT(logo_url)) as sem_logo
      FROM times
    `)
    
    console.log('\n📊 ESTATÍSTICAS:')
    console.log(`Campeonatos: ${statsCampeonatos[0].com_logo}/${statsCampeonatos[0].total} com logo`)
    console.log(`Times: ${statsTimes[0].com_logo}/${statsTimes[0].total} com logo`)
    
  } catch (error) {
    console.error('❌ Erro:', error)
  }
}

checkLogos()
