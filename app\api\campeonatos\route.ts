import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET(request: Request) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const status = searchParams.get("status") || "ativo"
    const pais = searchParams.get("pais")
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 20

    // Query simplificada para MySQL sem funções de data complexas
    let query = `
      SELECT
        c.*,
        0 as total_jogos,
        0 as jogos_futuros,
        0 as jogos_finalizados,
        0 as jogos_hoje,
        0 as total_times
      FROM campeonatos c
      WHERE 1=1
    `

    const params = []

    if (status && status !== 'todos') {
      query += ' AND c.status = ?'
      params.push(status)
    }

    if (pais && pais !== 'todos') {
      query += ' AND c.pais = ?'
      params.push(pais)
    }

    query += ' ORDER BY c.data_criacao DESC'

    if (limit && limit > 0) {
      query += ' LIMIT ?'
      params.push(limit)
    }

    const campeonatos = await executeQuery(query, params)

    // Buscar países disponíveis
    const paises = await executeQuery(`
      SELECT DISTINCT c.pais, COUNT(c.id) as total
      FROM campeonatos c
      WHERE c.status = 'ativo'
      GROUP BY c.pais
      ORDER BY 
        CASE WHEN c.pais = 'Brasil' THEN 0 ELSE 1 END,
        total DESC,
        c.pais ASC
    `)

    // Estatísticas gerais
    const stats = await executeQuery(`
      SELECT 
        COUNT(DISTINCT c.id) as total,
        COUNT(DISTINCT CASE WHEN c.status = 'ativo' THEN c.id END) as ativos,
        COUNT(DISTINCT CASE WHEN c.pais = 'Brasil' THEN c.id END) as nacionais,
        COUNT(DISTINCT CASE WHEN c.pais != 'Brasil' THEN c.id END) as internacionais
      FROM campeonatos c
    `)

    return NextResponse.json({
      success: true,
      campeonatos: campeonatos || [],
      total: campeonatos?.length || 0,
      paises: paises || [],
      stats: stats[0] || { total: 0, ativos: 0, nacionais: 0, internacionais: 0 },
      filters: {
        status,
        pais,
        limit
      }
    })

  } catch (error) {
    console.error("❌ Erro ao buscar campeonatos:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        campeonatos: [],
        total: 0,
        paises: [],
        stats: { total: 0, ativos: 0, nacionais: 0, internacionais: 0 }
      },
      { status: 500 }
    )
  }
}
