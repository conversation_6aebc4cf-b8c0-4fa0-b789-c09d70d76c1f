import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function cleanFakeData() {
  try {
    console.log('🧹 Removendo dados fictícios...')
    
    await initializeDatabase()
    
    // Remover bilhetes fictícios
    console.log('🎫 Removendo bilhetes fictícios...')
    const bilhetesResult = await executeQuery(`
      DELETE FROM bilhetes 
      WHERE codigo LIKE 'BLT%' 
      OR usuario_nome LIKE '%teste%' 
      OR usuario_nome LIKE '%Mario%'
      OR usuario_email LIKE '%teste%'
      OR usuario_email LIKE '%mario%'
    `)
    console.log(`✅ ${bilhetesResult.affectedRows} bilhetes fictícios removidos`)
    
    // Remover pagamentos fictícios
    console.log('💳 Removendo pagamentos fictícios...')
    const pagamentosResult = await executeQuery(`
      DELETE FROM pagamentos 
      WHERE codigo LIKE 'PAG%' 
      OR bilhete_codigo LIKE 'BLT%'
      OR status = 'Aprovado'
    `)
    console.log(`✅ ${pagamentosResult.affectedRows} pagamentos fictícios removidos`)
    
    // Remover apostas fictícias
    console.log('🎯 Removendo apostas fictícias...')
    const apostasResult = await executeQuery(`
      DELETE FROM apostas 
      WHERE bilhete_id IN (
        SELECT id FROM bilhetes 
        WHERE codigo LIKE 'BLT%' 
        OR usuario_nome LIKE '%teste%'
      )
    `)
    console.log(`✅ ${apostasResult.affectedRows} apostas fictícias removidas`)
    
    // Remover usuários de teste
    console.log('👤 Removendo usuários de teste...')
    const usuariosResult = await executeQuery(`
      DELETE FROM usuarios 
      WHERE nome LIKE '%teste%' 
      OR nome LIKE '%Mario%'
      OR email LIKE '%teste%'
      OR email LIKE '%mario%'
      OR cpf = '99999999999'
    `)
    console.log(`✅ ${usuariosResult.affectedRows} usuários de teste removidos`)
    
    // Resetar estatísticas
    console.log('📊 Resetando estatísticas...')
    
    // Verificar dados restantes
    const [bilhetesCount] = await executeQuery('SELECT COUNT(*) as count FROM bilhetes')
    const [pagamentosCount] = await executeQuery('SELECT COUNT(*) as count FROM pagamentos')
    const [apostasCount] = await executeQuery('SELECT COUNT(*) as count FROM apostas')
    const [usuariosCount] = await executeQuery('SELECT COUNT(*) as count FROM usuarios')
    
    console.log('\n📊 Dados restantes após limpeza:')
    console.log(`   🎫 Bilhetes: ${bilhetesCount.count}`)
    console.log(`   💳 Pagamentos: ${pagamentosCount.count}`)
    console.log(`   🎯 Apostas: ${apostasCount.count}`)
    console.log(`   👤 Usuários: ${usuariosCount.count}`)
    
    console.log('\n🎉 Limpeza de dados fictícios concluída!')
    
  } catch (error) {
    console.error('❌ Erro ao limpar dados fictícios:', error.message)
  }
}

// Executar
cleanFakeData()
