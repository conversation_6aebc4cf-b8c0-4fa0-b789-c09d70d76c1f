"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, Plus, Trophy, Globe, Loader2, AlertCircle } from "lucide-react"

interface Campeonato {
  id: number
  nome: string
  descricao: string
  pais: string
  temporada: string
  status: string
  data_inicio: string
  data_fim: string
}

interface Stats {
  ativos: number
  jogosHoje: number
  internacionais: number
}

export default function CampeonatosPage() {
  const [campeonatos, setCampeonatos] = useState<Campeonato[]>([])
  const [stats, setStats] = useState<Stats>({ ativos: 0, jogosHoje: 0, internacionais: 0 })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/admin/campeonatos")

      // Verificar se a resposta é válida
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Verificar se o conteúdo é JSON
      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text()
        console.error("Resposta não é JSON:", text)
        throw new Error("Resposta do servidor não é JSON válido")
      }

      const data = await response.json()

      if (data.success === false) {
        throw new Error(data.message || "Erro no servidor")
      }

      setCampeonatos(data.campeonatos || [])
      setStats(data.stats || { ativos: 0, jogosHoje: 0, internacionais: 0 })
    } catch (error) {
      console.error("Erro ao carregar campeonatos:", error)
      setError(error instanceof Error ? error.message : "Erro desconhecido")
      setCampeonatos([])
      setStats({ ativos: 0, jogosHoje: 0, internacionais: 0 })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Campeonatos</h1>
            <p className="text-gray-600 mt-2">Gerencie campeonatos e competições</p>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center flex-col space-y-4">
              <AlertCircle className="h-12 w-12 text-red-500" />
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900">Erro ao carregar campeonatos</h3>
                <p className="text-gray-600 mt-1">{error}</p>
                <Button onClick={fetchData} className="mt-4">
                  Tentar novamente
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Campeonatos</h1>
          <p className="text-gray-600 mt-2">Gerencie campeonatos e competições</p>
        </div>
        <Button className="bg-green-600 hover:bg-green-700">
          <Plus className="h-4 w-4 mr-2" />
          Novo Campeonato
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Campeonatos Ativos</p>
                <p className="text-3xl font-bold text-gray-900">{stats.ativos}</p>
              </div>
              <Trophy className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Jogos Hoje</p>
                <p className="text-3xl font-bold text-gray-900">{stats.jogosHoje}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Ligas Internacionais</p>
                <p className="text-3xl font-bold text-gray-900">{stats.internacionais}</p>
              </div>
              <Globe className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Campeonatos */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Campeonatos</CardTitle>
          <CardDescription>Todos os campeonatos cadastrados no sistema</CardDescription>
        </CardHeader>
        <CardContent>
          {campeonatos.length === 0 ? (
            <div className="text-center py-12">
              <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Nenhum campeonato encontrado</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {campeonatos.map((campeonato) => (
                <Card key={campeonato.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-lg">{campeonato.nome}</h3>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          campeonato.status === "ativo" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {campeonato.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{campeonato.descricao}</p>
                    <div className="text-xs text-gray-500">
                      <p>
                        <strong>País:</strong> {campeonato.pais}
                      </p>
                      <p>
                        <strong>Temporada:</strong> {campeonato.temporada}
                      </p>
                      {campeonato.data_inicio && (
                        <p>
                          <strong>Período:</strong> {new Date(campeonato.data_inicio).toLocaleDateString("pt-BR")} -{" "}
                          {new Date(campeonato.data_fim).toLocaleDateString("pt-BR")}
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
