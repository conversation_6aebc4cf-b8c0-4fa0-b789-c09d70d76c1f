import { executeQuery, initializeDatabase } from './lib/database.js'

async function createJogos() {
  try {
    console.log('🎮 Criando jogos...')
    
    await initializeDatabase()
    
    // Buscar campeonatos e times
    const campeonatos = await executeQuery('SELECT id, nome FROM campeonatos WHERE status = "ativo" LIMIT 5')
    const times = await executeQuery('SELECT id, nome, pais FROM times LIMIT 20')
    
    console.log(`📊 Encontrados ${campeonatos.length} campeonatos e ${times.length} times`)
    
    let totalJogos = 0
    
    for (const campeonato of campeonatos) {
      console.log(`🏆 Criando jogos para ${campeonato.nome}...`)
      
      // Filtrar times por país para cada campeonato
      let timesParaCampeonato = []
      
      if (campeonato.nome.includes('Brasileirão') || campeonato.nome.includes('Copa do Brasil')) {
        timesParaCampeonato = times.filter(t => t.pais === 'Brasil')
      } else if (campeonato.nome.includes('Premier League')) {
        timesParaCampeonato = times.filter(t => t.pais === 'Inglaterra')
      } else {
        // Para outros campeonatos, usar todos os times
        timesParaCampeonato = times
      }
      
      console.log(`  📋 ${timesParaCampeonato.length} times disponíveis`)
      
      // Criar 5 jogos por campeonato
      const numJogos = Math.min(5, Math.floor(timesParaCampeonato.length / 2))
      
      for (let i = 0; i < numJogos; i++) {
        const timeCasaIndex = (i * 2) % timesParaCampeonato.length
        const timeForaIndex = (i * 2 + 1) % timesParaCampeonato.length
        
        if (timeCasaIndex !== timeForaIndex && timesParaCampeonato[timeCasaIndex] && timesParaCampeonato[timeForaIndex]) {
          const dataJogo = new Date(Date.now() + (totalJogos + 1) * 24 * 60 * 60 * 1000) // Espalhar pelos próximos dias
          
          await executeQuery(`
            INSERT INTO jogos (campeonato_id, time_casa_id, time_fora_id, data_jogo, status, data_criacao)
            VALUES (?, ?, ?, ?, 'agendado', NOW())
          `, [
            campeonato.id,
            timesParaCampeonato[timeCasaIndex].id,
            timesParaCampeonato[timeForaIndex].id,
            dataJogo
          ])
          
          totalJogos++
          console.log(`    ⚽ ${timesParaCampeonato[timeCasaIndex].nome} vs ${timesParaCampeonato[timeForaIndex].nome}`)
        }
      }
    }
    
    console.log(`✅ ${totalJogos} jogos criados no total`)
    
    // Verificar jogos criados
    const jogosVerificacao = await executeQuery(`
      SELECT 
        j.id,
        c.nome as campeonato,
        tc.nome as time_casa,
        tf.nome as time_fora,
        j.data_jogo,
        j.status
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      ORDER BY j.data_jogo ASC
      LIMIT 10
    `)
    
    console.log('\n🎮 Primeiros 10 jogos criados:')
    jogosVerificacao.forEach(jogo => {
      const data = new Date(jogo.data_jogo).toLocaleDateString('pt-BR')
      console.log(`  ${jogo.id}: ${jogo.time_casa} vs ${jogo.time_fora} (${jogo.campeonato}) - ${data}`)
    })
    
    // Contar total de jogos
    const totalJogosResult = await executeQuery('SELECT COUNT(*) as count FROM jogos')
    console.log(`\n🎉 Total de jogos no banco: ${totalJogosResult[0].count}`)
    
  } catch (error) {
    console.error('❌ Erro:', error)
  }
}

createJogos()
