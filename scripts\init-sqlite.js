#!/usr/bin/env node

/**
 * Script para inicializar o banco SQLite do Sistema Bolão
 * Execute: node scripts/init-sqlite.js
 */

import { initializeDatabase, executeQuery, executeQuerySingle } from '../lib/database-sqlite.js'

async function main() {
  try {
    console.log('🚀 Inicializando Sistema Bolão - SQLite Setup')
    console.log('=' .repeat(50))
    
    // Inicializar banco
    await initializeDatabase()
    
    // Verificar dados
    const [userCount] = await executeQuery('SELECT COUNT(*) as count FROM usuarios')
    const [championshipCount] = await executeQuery('SELECT COUNT(*) as count FROM campeonatos')
    const [teamCount] = await executeQuery('SELECT COUNT(*) as count FROM times')
    
    console.log('\n📊 Dados iniciais:')
    console.log(`  - Usuários: ${userCount.count}`)
    console.log(`  - Campeonatos: ${championshipCount.count}`)
    console.log(`  - Times: ${teamCount.count}`)
    
    console.log('\n🎉 Inicialização do banco SQLite concluída com sucesso!')
    console.log('\n📝 Próximos passos:')
    console.log('  1. Execute: npm run sync:football (para sincronizar dados da API)')
    console.log('  2. Execute: npm run dev')
    console.log('  3. Acesse: http://localhost:3000')
    console.log('\n👤 Usuário admin padrão:')
    console.log('  Email: <EMAIL>')
    console.log('  Senha: admin123 (altere após o primeiro login)')
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Erro ao inicializar banco SQLite:', error)
    process.exit(1)
  }
}

main()
