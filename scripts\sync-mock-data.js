#!/usr/bin/env node

/**
 * Script de sincronização com dados mock para demonstração
 * Usado quando a API externa não está disponível
 */

import { config } from 'dotenv'
import { initializeDatabase, executeQuery, executeQuerySingle } from '../lib/database-sqlite.js'

// Carregar variáveis de ambiente
config({ path: '.env.local' })

// Dados mock das principais competições
const MOCK_COMPETITIONS = [
  {
    id: 2021,
    name: "Premier League",
    code: "PL",
    type: "LEAGUE",
    emblem: "https://crests.football-data.org/PL.png",
    area: { name: "England" },
    currentSeason: {
      startDate: "2024-08-17",
      endDate: "2025-05-25"
    }
  },
  {
    id: 2014,
    name: "La Liga",
    code: "PD",
    type: "LEAGUE", 
    emblem: "https://crests.football-data.org/PD.png",
    area: { name: "Spain" },
    currentSeason: {
      startDate: "2024-08-18",
      endDate: "2025-05-25"
    }
  },
  {
    id: 2019,
    name: "Serie A",
    code: "SA",
    type: "LEAGUE",
    emblem: "https://crests.football-data.org/SA.png", 
    area: { name: "Italy" },
    currentSeason: {
      startDate: "2024-08-18",
      endDate: "2025-05-25"
    }
  },
  {
    id: 2002,
    name: "Bundesliga",
    code: "BL1",
    type: "LEAGUE",
    emblem: "https://crests.football-data.org/BL1.png",
    area: { name: "Germany" },
    currentSeason: {
      startDate: "2024-08-24",
      endDate: "2025-05-17"
    }
  },
  {
    id: 2015,
    name: "Ligue 1",
    code: "FL1", 
    type: "LEAGUE",
    emblem: "https://crests.football-data.org/FL1.png",
    area: { name: "France" },
    currentSeason: {
      startDate: "2024-08-16",
      endDate: "2025-05-18"
    }
  },
  {
    id: 2001,
    name: "UEFA Champions League",
    code: "CL",
    type: "CUP",
    emblem: "https://crests.football-data.org/CL.png",
    area: { name: "Europe" },
    currentSeason: {
      startDate: "2024-09-17",
      endDate: "2025-05-31"
    }
  },
  {
    id: 2018,
    name: "UEFA Europa League", 
    code: "EL",
    type: "CUP",
    emblem: "https://crests.football-data.org/EL.png",
    area: { name: "Europe" },
    currentSeason: {
      startDate: "2024-09-25",
      endDate: "2025-05-21"
    }
  },
  {
    id: 2013,
    name: "Campeonato Brasileiro Série A",
    code: "BSA",
    type: "LEAGUE",
    emblem: "https://crests.football-data.org/764.svg",
    area: { name: "Brazil" },
    currentSeason: {
      startDate: "2025-04-12",
      endDate: "2025-12-07"
    }
  }
]

// Times mock para cada competição
const MOCK_TEAMS = {
  2021: [ // Premier League
    { id: 57, name: "Arsenal", shortName: "Arsenal", tla: "ARS", crest: "https://crests.football-data.org/57.png", area: { name: "England" } },
    { id: 65, name: "Manchester City", shortName: "Man City", tla: "MCI", crest: "https://crests.football-data.org/65.png", area: { name: "England" } },
    { id: 64, name: "Liverpool", shortName: "Liverpool", tla: "LIV", crest: "https://crests.football-data.org/64.png", area: { name: "England" } },
    { id: 61, name: "Chelsea", shortName: "Chelsea", tla: "CHE", crest: "https://crests.football-data.org/61.png", area: { name: "England" } },
    { id: 66, name: "Manchester United", shortName: "Man United", tla: "MUN", crest: "https://crests.football-data.org/66.png", area: { name: "England" } }
  ],
  2014: [ // La Liga
    { id: 81, name: "FC Barcelona", shortName: "Barcelona", tla: "BAR", crest: "https://crests.football-data.org/81.png", area: { name: "Spain" } },
    { id: 86, name: "Real Madrid CF", shortName: "Real Madrid", tla: "RMA", crest: "https://crests.football-data.org/86.png", area: { name: "Spain" } },
    { id: 78, name: "Atlético Madrid", shortName: "Atlético", tla: "ATM", crest: "https://crests.football-data.org/78.png", area: { name: "Spain" } }
  ],
  2019: [ // Serie A
    { id: 109, name: "Juventus", shortName: "Juventus", tla: "JUV", crest: "https://crests.football-data.org/109.png", area: { name: "Italy" } },
    { id: 98, name: "AC Milan", shortName: "Milan", tla: "MIL", crest: "https://crests.football-data.org/98.png", area: { name: "Italy" } },
    { id: 108, name: "Inter", shortName: "Inter", tla: "INT", crest: "https://crests.football-data.org/108.png", area: { name: "Italy" } }
  ],
  2002: [ // Bundesliga
    { id: 5, name: "FC Bayern München", shortName: "Bayern", tla: "FCB", crest: "https://crests.football-data.org/5.png", area: { name: "Germany" } },
    { id: 4, name: "Borussia Dortmund", shortName: "Dortmund", tla: "BVB", crest: "https://crests.football-data.org/4.png", area: { name: "Germany" } }
  ],
  2015: [ // Ligue 1
    { id: 524, name: "Paris Saint-Germain", shortName: "PSG", tla: "PSG", crest: "https://crests.football-data.org/524.png", area: { name: "France" } }
  ],
  2013: [ // Brasileirão
    { id: 1776, name: "Flamengo", shortName: "Flamengo", tla: "FLA", crest: "https://crests.football-data.org/1776.png", area: { name: "Brazil" } },
    { id: 1777, name: "Palmeiras", shortName: "Palmeiras", tla: "PAL", crest: "https://crests.football-data.org/1777.png", area: { name: "Brazil" } },
    { id: 1778, name: "São Paulo", shortName: "São Paulo", tla: "SAO", crest: "https://crests.football-data.org/1778.png", area: { name: "Brazil" } },
    { id: 1779, name: "Corinthians", shortName: "Corinthians", tla: "COR", crest: "https://crests.football-data.org/1779.png", area: { name: "Brazil" } }
  ]
}

function getCurrentSeason() {
  return "2025"
}

async function syncMockCompetitions() {
  try {
    console.log('🏆 Sincronizando campeonatos mock...')
    
    let syncedCount = 0
    
    for (const competition of MOCK_COMPETITIONS) {
      try {
        // Verificar se já existe
        const existing = await executeQuerySingle(
          'SELECT id FROM campeonatos WHERE api_id = ?',
          [competition.id.toString()]
        )

        const competitionData = {
          nome: competition.name,
          descricao: `${competition.name} - ${competition.area?.name || 'Internacional'}`,
          pais: competition.area?.name || 'Internacional',
          temporada: getCurrentSeason(),
          status: 'ativo',
          data_inicio: competition.currentSeason?.startDate || null,
          data_fim: competition.currentSeason?.endDate || null,
          api_id: competition.id.toString(),
          logo_url: competition.emblem || null,
          codigo: competition.code || null,
          tipo: competition.type || 'LEAGUE',
          plano: 'TIER_ONE'
        }

        if (existing) {
          await executeQuery(`
            UPDATE campeonatos SET 
              nome = ?, descricao = ?, pais = ?, temporada = ?, 
              data_inicio = ?, data_fim = ?, logo_url = ?, codigo = ?, tipo = ?, plano = ?
            WHERE api_id = ?
          `, [
            competitionData.nome, competitionData.descricao, competitionData.pais,
            competitionData.temporada, competitionData.data_inicio, competitionData.data_fim,
            competitionData.logo_url, competitionData.codigo, competitionData.tipo,
            competitionData.plano, competitionData.api_id
          ])
          console.log(`✅ Atualizado: ${competition.name}`)
        } else {
          await executeQuery(`
            INSERT INTO campeonatos (nome, descricao, pais, temporada, status, data_inicio, data_fim, api_id, logo_url, codigo, tipo, plano)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            competitionData.nome, competitionData.descricao, competitionData.pais,
            competitionData.temporada, competitionData.status, competitionData.data_inicio,
            competitionData.data_fim, competitionData.api_id, competitionData.logo_url,
            competitionData.codigo, competitionData.tipo, competitionData.plano
          ])
          console.log(`➕ Adicionado: ${competition.name}`)
        }
        
        syncedCount++
        
      } catch (error) {
        console.error(`❌ Erro ao sincronizar ${competition.name}:`, error.message)
      }
    }

    console.log(`✅ Sincronizados ${syncedCount} campeonatos mock`)
    return syncedCount

  } catch (error) {
    console.error('❌ Erro ao sincronizar campeonatos mock:', error)
    throw error
  }
}

async function syncMockTeams() {
  try {
    console.log('👥 Sincronizando times mock...')
    
    let totalTeams = 0
    
    for (const [competitionApiId, teams] of Object.entries(MOCK_TEAMS)) {
      try {
        // Buscar competição no banco
        const competition = await executeQuerySingle(
          'SELECT id, nome FROM campeonatos WHERE api_id = ?',
          [competitionApiId]
        )
        
        if (!competition) {
          console.log(`⚠️ Competição ${competitionApiId} não encontrada`)
          continue
        }
        
        console.log(`📋 Processando ${teams.length} times para ${competition.nome}`)
        
        for (const team of teams) {
          try {
            const existing = await executeQuerySingle(
              'SELECT id FROM times WHERE api_id = ?',
              [team.id.toString()]
            )

            const teamData = {
              nome: team.name,
              nome_curto: team.shortName || team.tla,
              pais: team.area?.name || null,
              logo_url: team.crest || null,
              api_id: team.id.toString()
            }

            if (existing) {
              await executeQuery(`
                UPDATE times SET 
                  nome = ?, nome_curto = ?, pais = ?, logo_url = ?, data_atualizacao = CURRENT_TIMESTAMP
                WHERE api_id = ?
              `, [teamData.nome, teamData.nome_curto, teamData.pais, teamData.logo_url, teamData.api_id])
            } else {
              await executeQuery(`
                INSERT INTO times (nome, nome_curto, pais, logo_url, api_id)
                VALUES (?, ?, ?, ?, ?)
              `, [teamData.nome, teamData.nome_curto, teamData.pais, teamData.logo_url, teamData.api_id])
            }
            
            totalTeams++
            
          } catch (error) {
            console.error(`❌ Erro ao sincronizar time ${team.name}:`, error.message)
          }
        }
        
      } catch (error) {
        console.error(`❌ Erro ao processar competição ${competitionApiId}:`, error.message)
      }
    }

    console.log(`✅ Sincronizados ${totalTeams} times mock`)
    return totalTeams

  } catch (error) {
    console.error('❌ Erro ao sincronizar times mock:', error)
    throw error
  }
}

async function syncMockMatches() {
  try {
    console.log('⚽ Criando partidas mock...')
    
    // Buscar competições e times
    const competitions = await executeQuery(`
      SELECT id, api_id, nome FROM campeonatos 
      WHERE status = 'ativo' AND api_id IS NOT NULL
      LIMIT 3
    `)
    
    let totalMatches = 0
    
    for (const competition of competitions) {
      try {
        // Buscar times da competição
        const teams = await executeQuery(`
          SELECT DISTINCT t.* FROM times t
          WHERE t.api_id IN (${MOCK_TEAMS[competition.api_id]?.map(() => '?').join(',') || '?'})
        `, MOCK_TEAMS[competition.api_id]?.map(t => t.id.toString()) || ['0'])
        
        if (teams.length < 2) continue
        
        // Criar algumas partidas mock
        for (let i = 0; i < Math.min(teams.length - 1, 5); i++) {
          try {
            const homeTeam = teams[i]
            const awayTeam = teams[i + 1]
            
            const matchDate = new Date()
            matchDate.setDate(matchDate.getDate() + i + 1) // Próximos dias
            
            const matchData = {
              campeonato_id: competition.id,
              time_casa_id: homeTeam.id,
              time_fora_id: awayTeam.id,
              data_jogo: matchDate.toISOString(),
              rodada: i + 1,
              status: 'agendado',
              api_id: `mock_${competition.api_id}_${i + 1}`
            }
            
            // Verificar se já existe
            const existing = await executeQuerySingle(
              'SELECT id FROM jogos WHERE api_id = ?',
              [matchData.api_id]
            )
            
            if (!existing) {
              await executeQuery(`
                INSERT INTO jogos (campeonato_id, time_casa_id, time_fora_id, data_jogo, rodada, status, api_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
              `, [
                matchData.campeonato_id, matchData.time_casa_id, matchData.time_fora_id,
                matchData.data_jogo, matchData.rodada, matchData.status, matchData.api_id
              ])
              
              totalMatches++
            }
            
          } catch (error) {
            console.error(`❌ Erro ao criar partida mock:`, error.message)
          }
        }
        
      } catch (error) {
        console.error(`❌ Erro ao processar partidas de ${competition.nome}:`, error.message)
      }
    }

    console.log(`✅ Criadas ${totalMatches} partidas mock`)
    return totalMatches

  } catch (error) {
    console.error('❌ Erro ao criar partidas mock:', error)
    throw error
  }
}

async function main() {
  try {
    console.log('🚀 Iniciando sincronização com dados MOCK...')
    console.log('📅 Temporada:', getCurrentSeason())
    console.log('⏰ Início:', new Date().toLocaleString('pt-BR'))
    
    await initializeDatabase()
    
    // 1. Sincronizar campeonatos
    const competitionsCount = await syncMockCompetitions()
    
    // 2. Sincronizar times
    const teamsCount = await syncMockTeams()
    
    // 3. Criar partidas
    const matchesCount = await syncMockMatches()
    
    console.log('\n🎉 Sincronização MOCK concluída!')
    console.log('=' .repeat(50))
    console.log(`📊 RESUMO:`)
    console.log(`   🏆 Campeonatos: ${competitionsCount}`)
    console.log(`   👥 Times: ${teamsCount}`)
    console.log(`   ⚽ Partidas: ${matchesCount}`)
    console.log(`   🏁 Término: ${new Date().toLocaleString('pt-BR')}`)
    
    return {
      success: true,
      competitions: competitionsCount,
      teams: teamsCount,
      matches: matchesCount
    }
    
  } catch (error) {
    console.error('❌ Erro na sincronização mock:', error)
    throw error
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(() => {
    console.log('✅ Processo finalizado com sucesso!')
    process.exit(0)
  }).catch(error => {
    console.error('💥 Processo finalizado com erro:', error.message)
    process.exit(1)
  })
}

export { main as syncMockData }
