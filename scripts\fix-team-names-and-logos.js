import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function fixTeamNamesAndLogos() {
  try {
    console.log('🔧 Corrigindo nomes e logos dos times...')
    
    await initializeDatabase()
    
    // Primeiro, vamos ver quais times temos
    const times = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url FROM times 
      ORDER BY nome
    `)
    
    console.log(`📊 Total de times encontrados: ${times.length}`)
    
    // Mapeamento correto de times com logos oficiais
    const teamCorrections = [
      // Times brasileiros principais
      { 
        searchNames: ['Flamengo', 'Clube de Regatas do Flamengo'], 
        correctName: 'Flamengo',
        shortName: 'Flamengo',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/flamengo-vector-logo.png'
      },
      { 
        searchNames: ['Palmeiras', 'Sociedade Esportiva Palmeiras'], 
        correctName: 'Palmeiras',
        shortName: 'Palmeiras',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/palmeiras-vector-logo.png'
      },
      { 
        searchNames: ['São Paulo', 'São Paulo Futebol Clube'], 
        correctName: 'São Paulo',
        shortName: 'São Paulo',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/sao-paulo-vector-logo.png'
      },
      { 
        searchNames: ['Corinthians', 'Sport Club Corinthians Paulista'], 
        correctName: 'Corinthians',
        shortName: 'Corinthians',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/corinthians-vector-logo.png'
      },
      { 
        searchNames: ['Santos', 'Santos Futebol Clube'], 
        correctName: 'Santos',
        shortName: 'Santos',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/santos-vector-logo.png'
      },
      { 
        searchNames: ['Grêmio', 'Grêmio Foot-Ball Porto Alegrense'], 
        correctName: 'Grêmio',
        shortName: 'Grêmio',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/gremio-vector-logo.png'
      },
      { 
        searchNames: ['Internacional', 'Sport Club Internacional'], 
        correctName: 'Internacional',
        shortName: 'Internacional',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/internacional-vector-logo.png'
      },
      { 
        searchNames: ['Atlético Mineiro', 'Clube Atlético Mineiro'], 
        correctName: 'Atlético Mineiro',
        shortName: 'Atlético-MG',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/atletico-mineiro-vector-logo.png'
      },
      { 
        searchNames: ['Cruzeiro', 'Cruzeiro Esporte Clube'], 
        correctName: 'Cruzeiro',
        shortName: 'Cruzeiro',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/cruzeiro-vector-logo.png'
      },
      { 
        searchNames: ['Botafogo', 'Botafogo de Futebol e Regatas'], 
        correctName: 'Botafogo',
        shortName: 'Botafogo',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/botafogo-vector-logo.png'
      },
      { 
        searchNames: ['Vasco', 'Club de Regatas Vasco da Gama'], 
        correctName: 'Vasco da Gama',
        shortName: 'Vasco',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/vasco-da-gama-vector-logo.png'
      },
      { 
        searchNames: ['Fluminense', 'Fluminense Football Club'], 
        correctName: 'Fluminense',
        shortName: 'Fluminense',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/fluminense-vector-logo.png'
      },
      { 
        searchNames: ['Bahia', 'Bahia Esporte Clube'], 
        correctName: 'Bahia',
        shortName: 'Bahia',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/EC_Bahia_logo.svg/200px-EC_Bahia_logo.svg.png'
      },
      { 
        searchNames: ['Fortaleza', 'Fortaleza Esporte Clube'], 
        correctName: 'Fortaleza',
        shortName: 'Fortaleza',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/Fortaleza_Esporte_Clube_logo.svg/200px-Fortaleza_Esporte_Clube_logo.svg.png'
      },
      { 
        searchNames: ['Coritiba', 'Coritiba Foot Ball Club'], 
        correctName: 'Coritiba',
        shortName: 'Coritiba',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2b/Coritiba_Foot_Ball_Club_logo.svg/200px-Coritiba_Foot_Ball_Club_logo.svg.png'
      },
      { 
        searchNames: ['Athletico', 'Athletico Paranaense'], 
        correctName: 'Athletico Paranaense',
        shortName: 'Athletico-PR',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/56/Club_Athletico_Paranaense.svg/200px-Club_Athletico_Paranaense.svg.png'
      },
      
      // Times internacionais famosos
      { 
        searchNames: ['Barcelona', 'FC Barcelona'], 
        correctName: 'FC Barcelona',
        shortName: 'Barcelona',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/4/47/FC_Barcelona_%28crest%29.svg/200px-FC_Barcelona_%28crest%29.svg.png'
      },
      { 
        searchNames: ['Real Madrid', 'Real Madrid CF'], 
        correctName: 'Real Madrid',
        shortName: 'Real Madrid',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/5/56/Real_Madrid_CF.svg/200px-Real_Madrid_CF.svg.png'
      },
      { 
        searchNames: ['Atlético Madrid', 'Club Atlético de Madrid'], 
        correctName: 'Atlético Madrid',
        shortName: 'Atlético Madrid',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/c/c1/Atletico_Madrid_logo.svg/200px-Atletico_Madrid_logo.svg.png'
      },
      { 
        searchNames: ['Manchester United', 'Manchester United FC'], 
        correctName: 'Manchester United',
        shortName: 'Manchester United',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/7/7a/Manchester_United_FC_crest.svg/200px-Manchester_United_FC_crest.svg.png'
      },
      { 
        searchNames: ['Manchester City', 'Manchester City FC'], 
        correctName: 'Manchester City',
        shortName: 'Manchester City',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/e/eb/Manchester_City_FC_badge.svg/200px-Manchester_City_FC_badge.svg.png'
      },
      { 
        searchNames: ['Liverpool', 'Liverpool FC'], 
        correctName: 'Liverpool',
        shortName: 'Liverpool',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/0/0c/Liverpool_FC.svg/200px-Liverpool_FC.svg.png'
      },
      { 
        searchNames: ['Chelsea', 'Chelsea FC'], 
        correctName: 'Chelsea',
        shortName: 'Chelsea',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/c/cc/Chelsea_FC.svg/200px-Chelsea_FC.svg.png'
      },
      { 
        searchNames: ['Arsenal', 'Arsenal FC'], 
        correctName: 'Arsenal',
        shortName: 'Arsenal',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/5/53/Arsenal_FC.svg/200px-Arsenal_FC.svg.png'
      },
      { 
        searchNames: ['Juventus', 'Juventus FC'], 
        correctName: 'Juventus',
        shortName: 'Juventus',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/15/Juventus_FC_2017_logo.svg/200px-Juventus_FC_2017_logo.svg.png'
      },
      { 
        searchNames: ['Inter', 'Inter Milan', 'FC Internazionale Milano'], 
        correctName: 'Inter Milan',
        shortName: 'Inter',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/FC_Internazionale_Milano_2021.svg/200px-FC_Internazionale_Milano_2021.svg.png'
      },
      { 
        searchNames: ['AC Milan', 'Milan'], 
        correctName: 'AC Milan',
        shortName: 'Milan',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/Logo_of_AC_Milan.svg/200px-Logo_of_AC_Milan.svg.png'
      },
      { 
        searchNames: ['Bayern Munich', 'FC Bayern München'], 
        correctName: 'Bayern Munich',
        shortName: 'Bayern',
        logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg/200px-FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg.png'
      },
      { 
        searchNames: ['Paris Saint-Germain', 'PSG'], 
        correctName: 'Paris Saint-Germain',
        shortName: 'PSG',
        logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/a/a7/Paris_Saint-Germain_F.C..svg/200px-Paris_Saint-Germain_F.C..svg.png'
      }
    ]
    
    let updatedCount = 0
    
    // Corrigir cada time
    for (const correction of teamCorrections) {
      for (const searchName of correction.searchNames) {
        try {
          const result = await executeQuery(`
            UPDATE times 
            SET nome = ?, nome_curto = ?, logo_url = ?
            WHERE nome LIKE ? OR nome_curto LIKE ?
          `, [
            correction.correctName,
            correction.shortName,
            correction.logo,
            `%${searchName}%`,
            `%${searchName}%`
          ])
          
          if (result.affectedRows > 0) {
            console.log(`✅ ${searchName} -> ${correction.shortName} (${result.affectedRows} times atualizados)`)
            updatedCount += result.affectedRows
          }
        } catch (error) {
          console.error(`❌ Erro ao atualizar ${searchName}:`, error.message)
        }
      }
    }
    
    console.log(`\n🎯 Total de times atualizados: ${updatedCount}`)
    
    // Verificar estatísticas finais
    const [stats] = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN logo_url IS NOT NULL AND logo_url != '' THEN 1 ELSE 0 END) as com_logo,
        SUM(CASE WHEN logo_url IS NULL OR logo_url = '' THEN 1 ELSE 0 END) as sem_logo
      FROM times
    `)
    
    console.log(`\n📊 Estatísticas finais:`)
    console.log(`   📈 Total de times: ${stats.total}`)
    console.log(`   ✅ Times com logo: ${stats.com_logo}`)
    console.log(`   ❌ Times sem logo: ${stats.sem_logo}`)
    
    // Mostrar alguns exemplos de times atualizados
    const exemplos = await executeQuery(`
      SELECT nome, nome_curto, logo_url FROM times 
      WHERE logo_url IS NOT NULL AND logo_url != ''
      ORDER BY nome
      LIMIT 10
    `)
    
    console.log(`\n🎨 Exemplos de times com logos:`)
    exemplos.forEach(time => {
      console.log(`   ${time.nome} -> ${time.nome_curto}`)
    })
    
  } catch (error) {
    console.error('❌ Erro ao corrigir times:', error.message)
  }
}

// Executar
fixTeamNamesAndLogos()
