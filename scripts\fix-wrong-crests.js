import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function fixWrongCrests() {
  try {
    console.log('🔧 Corrigindo escudos errados dos times...')
    
    await initializeDatabase()
    
    // Correções específicas de escudos errados
    const corrections = [
      // Santos com escudo correto
      {
        teamName: 'Santos',
        correctCrest: 'https://crests.football-data.org/1779.png',
        wrongCrest: 'https://crests.football-data.org/1778.png' // Escudo do Corinthians
      },
      // Corinthians com escudo correto
      {
        teamName: 'Corinthians',
        correctCrest: 'https://crests.football-data.org/1778.png',
        wrongCrest: null
      },
      // Flamengo com escudo correto
      {
        teamName: 'Flamengo',
        correctCrest: 'https://crests.football-data.org/1776.png',
        wrongCrest: null
      },
      // Palmeiras com escudo correto
      {
        teamName: 'Palmeiras',
        correctCrest: 'https://crests.football-data.org/1769.png',
        wrongCrest: null
      },
      // São Paulo com escudo correto
      {
        teamName: 'São Paulo',
        correctCrest: 'https://crests.football-data.org/1777.png',
        wrongCrest: null
      },
      // Grêmio com escudo correto
      {
        teamName: 'Grêmio',
        correctCrest: 'https://crests.football-data.org/1767.png',
        wrongCrest: null
      },
      // Internacional com escudo correto
      {
        teamName: 'Internacional',
        correctCrest: 'https://crests.football-data.org/1768.png',
        wrongCrest: null
      },
      // Atlético Mineiro com escudo correto
      {
        teamName: 'Atlético Mineiro',
        correctCrest: 'https://crests.football-data.org/1766.png',
        wrongCrest: null
      },
      // Cruzeiro com escudo correto
      {
        teamName: 'Cruzeiro',
        correctCrest: 'https://crests.football-data.org/1771.png',
        wrongCrest: null
      },
      // Botafogo com escudo correto
      {
        teamName: 'Botafogo',
        correctCrest: 'https://crests.football-data.org/1770.png',
        wrongCrest: null
      },
      // Vasco com escudo correto
      {
        teamName: 'Vasco',
        correctCrest: 'https://crests.football-data.org/1772.png',
        wrongCrest: null
      },
      // Fluminense com escudo correto
      {
        teamName: 'Fluminense',
        correctCrest: 'https://crests.football-data.org/1765.png',
        wrongCrest: null
      }
    ]
    
    let fixedCount = 0
    
    for (const correction of corrections) {
      try {
        // Atualizar todos os times com esse nome para o escudo correto
        const result = await executeQuery(`
          UPDATE times 
          SET logo_url = ?
          WHERE (nome LIKE ? OR nome_curto LIKE ?)
        `, [
          correction.correctCrest,
          `%${correction.teamName}%`,
          `%${correction.teamName}%`
        ])
        
        if (result.affectedRows > 0) {
          console.log(`✅ ${correction.teamName}: ${result.affectedRows} times corrigidos -> ${correction.correctCrest}`)
          fixedCount += result.affectedRows
        }
      } catch (error) {
        console.error(`❌ Erro ao corrigir ${correction.teamName}:`, error.message)
      }
    }
    
    console.log(`\n🔧 Total de escudos corrigidos: ${fixedCount}`)
    
    // Verificar se ainda há duplicatas
    const duplicates = await executeQuery(`
      SELECT nome, COUNT(*) as count 
      FROM times 
      GROUP BY nome 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `)
    
    if (duplicates.length > 0) {
      console.log(`\n⚠️ Times duplicados encontrados:`)
      duplicates.forEach(dup => {
        console.log(`   ${dup.nome}: ${dup.count} registros`)
      })
      
      // Remover duplicatas, mantendo apenas o primeiro registro de cada time
      for (const dup of duplicates) {
        try {
          // Manter apenas o registro com menor ID (mais antigo)
          await executeQuery(`
            DELETE FROM times 
            WHERE nome = ? 
            AND id NOT IN (
              SELECT * FROM (
                SELECT MIN(id) FROM times WHERE nome = ?
              ) as temp
            )
          `, [dup.nome, dup.nome])
          
          console.log(`🗑️ Removidas ${dup.count - 1} duplicatas de ${dup.nome}`)
        } catch (error) {
          console.error(`❌ Erro ao remover duplicatas de ${dup.nome}:`, error.message)
        }
      }
    }
    
    // Verificar estatísticas finais
    const [stats] = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN logo_url IS NOT NULL AND logo_url != '' THEN 1 ELSE 0 END) as com_logo,
        SUM(CASE WHEN logo_url LIKE '%crests.football-data.org%' THEN 1 ELSE 0 END) as com_escudo_real
      FROM times
    `)
    
    console.log(`\n📊 Estatísticas finais:`)
    console.log(`   📈 Total de times: ${stats.total}`)
    console.log(`   ✅ Times com logo: ${stats.com_logo}`)
    console.log(`   🏆 Times com escudo real: ${stats.com_escudo_real}`)
    
    // Mostrar alguns exemplos de times principais com escudos corretos
    const principais = await executeQuery(`
      SELECT nome, nome_curto, logo_url FROM times 
      WHERE nome IN ('Santos', 'Corinthians', 'Flamengo', 'Palmeiras', 'São Paulo', 'Grêmio', 'Internacional')
      ORDER BY nome
    `)
    
    console.log(`\n🎨 Times principais com escudos corretos:`)
    principais.forEach(time => {
      console.log(`   ${time.nome_curto} -> ${time.logo_url}`)
    })
    
  } catch (error) {
    console.error('❌ Erro ao corrigir escudos:', error.message)
  }
}

// Executar
fixWrongCrests()
