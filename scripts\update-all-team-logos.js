import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function updateAllTeamLogos() {
  try {
    console.log('🎨 Atualizando logos de todos os times...')
    
    await initializeDatabase()
    
    // Logos dos times brasileiros mais conhecidos
    const teamLogos = [
      // Times do Rio de Janeiro
      { name: 'Flame<PERSON>', logo: 'https://logoeps.com/wp-content/uploads/2013/03/flamengo-vector-logo.png', short: 'Flamengo' },
      { name: 'Fluminense', logo: 'https://logoeps.com/wp-content/uploads/2013/03/fluminense-vector-logo.png', short: 'Fluminense' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', logo: 'https://logoeps.com/wp-content/uploads/2013/03/botafogo-vector-logo.png', short: 'Bo<PERSON>fo<PERSON>' },
      { name: 'Vasco', logo: 'https://logoeps.com/wp-content/uploads/2013/03/vasco-da-gama-vector-logo.png', short: 'Vasco' },
      
      // Times de São Paulo
      { name: 'Corinthians', logo: 'https://logoeps.com/wp-content/uploads/2013/03/corinthians-vector-logo.png', short: 'Corinthians' },
      { name: 'Palmeiras', logo: 'https://logoeps.com/wp-content/uploads/2013/03/palmeiras-vector-logo.png', short: 'Palmeiras' },
      { name: 'São Paulo', logo: 'https://logoeps.com/wp-content/uploads/2013/03/sao-paulo-vector-logo.png', short: 'São Paulo' },
      { name: 'Santos', logo: 'https://logoeps.com/wp-content/uploads/2013/03/santos-vector-logo.png', short: 'Santos' },
      
      // Times de Minas Gerais
      { name: 'Atlético Mineiro', logo: 'https://logoeps.com/wp-content/uploads/2013/03/atletico-mineiro-vector-logo.png', short: 'Atlético-MG' },
      { name: 'Cruzeiro', logo: 'https://logoeps.com/wp-content/uploads/2013/03/cruzeiro-vector-logo.png', short: 'Cruzeiro' },
      
      // Times do Rio Grande do Sul
      { name: 'Grêmio', logo: 'https://logoeps.com/wp-content/uploads/2013/03/gremio-vector-logo.png', short: 'Grêmio' },
      { name: 'Internacional', logo: 'https://logoeps.com/wp-content/uploads/2013/03/internacional-vector-logo.png', short: 'Internacional' },
      
      // Outros times brasileiros importantes
      { name: 'Bahia', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/EC_Bahia_logo.svg/200px-EC_Bahia_logo.svg.png', short: 'Bahia' },
      { name: 'Fortaleza', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/Fortaleza_Esporte_Clube_logo.svg/200px-Fortaleza_Esporte_Clube_logo.svg.png', short: 'Fortaleza' },
      { name: 'Ceará', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Ceara_sporting_club_logo.svg/200px-Ceara_sporting_club_logo.svg.png', short: 'Ceará' },
      { name: 'Sport', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/54/Sport_Club_do_Recife_logo.svg/200px-Sport_Club_do_Recife_logo.svg.png', short: 'Sport' },
      { name: 'Vitória', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/70/Esporte_Clube_Vit%C3%B3ria_logo.svg/200px-Esporte_Clube_Vit%C3%B3ria_logo.svg.png', short: 'Vitória' },
      { name: 'Coritiba', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2b/Coritiba_Foot_Ball_Club_logo.svg/200px-Coritiba_Foot_Ball_Club_logo.svg.png', short: 'Coritiba' },
      { name: 'Athletico', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/56/Club_Athletico_Paranaense.svg/200px-Club_Athletico_Paranaense.svg.png', short: 'Athletico-PR' },
      { name: 'Goiás', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Goi%C3%A1s_Esporte_Clube_logo.svg/200px-Goi%C3%A1s_Esporte_Clube_logo.svg.png', short: 'Goiás' },
      { name: 'Atlético Goianiense', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c2/Atl%C3%A9tico_Clube_Goianiense_logo.svg/200px-Atl%C3%A9tico_Clube_Goianiense_logo.svg.png', short: 'Atlético-GO' },
      { name: 'Cuiabá', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Cuiab%C3%A1_Esporte_Clube_logo.svg/200px-Cuiab%C3%A1_Esporte_Clube_logo.svg.png', short: 'Cuiabá' },
      { name: 'Bragantino', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/9b/Red_Bull_Bragantino_logo.svg/200px-Red_Bull_Bragantino_logo.svg.png', short: 'Bragantino' },
      { name: 'América Mineiro', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Am%C3%A9rica_Futebol_Clube_%28MG%29_logo.svg/200px-Am%C3%A9rica_Futebol_Clube_%28MG%29_logo.svg.png', short: 'América-MG' },
      { name: 'Avaí', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/Ava%C3%AD_FC_logo.svg/200px-Ava%C3%AD_FC_logo.svg.png', short: 'Avaí' },
      { name: 'Chapecoense', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/93/Chapecoense_logo.svg/200px-Chapecoense_logo.svg.png', short: 'Chapecoense' },
      { name: 'Juventude', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e4/Esporte_Clube_Juventude_logo.svg/200px-Esporte_Clube_Juventude_logo.svg.png', short: 'Juventude' },
      
      // Times internacionais famosos
      { name: 'Barcelona', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/4/47/FC_Barcelona_%28crest%29.svg/200px-FC_Barcelona_%28crest%29.svg.png', short: 'Barcelona' },
      { name: 'Real Madrid', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/5/56/Real_Madrid_CF.svg/200px-Real_Madrid_CF.svg.png', short: 'Real Madrid' },
      { name: 'Manchester United', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/7/7a/Manchester_United_FC_crest.svg/200px-Manchester_United_FC_crest.svg.png', short: 'Man United' },
      { name: 'Manchester City', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/e/eb/Manchester_City_FC_badge.svg/200px-Manchester_City_FC_badge.svg.png', short: 'Man City' },
      { name: 'Liverpool', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/0/0c/Liverpool_FC.svg/200px-Liverpool_FC.svg.png', short: 'Liverpool' },
      { name: 'Chelsea', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/c/cc/Chelsea_FC.svg/200px-Chelsea_FC.svg.png', short: 'Chelsea' },
      { name: 'Arsenal', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/5/53/Arsenal_FC.svg/200px-Arsenal_FC.svg.png', short: 'Arsenal' },
      { name: 'Tottenham', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/b/b4/Tottenham_Hotspur.svg/200px-Tottenham_Hotspur.svg.png', short: 'Tottenham' },
      { name: 'Bayern Munich', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg/200px-FC_Bayern_M%C3%BCnchen_logo_%282017%29.svg.png', short: 'Bayern' },
      { name: 'Borussia Dortmund', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/67/Borussia_Dortmund_logo.svg/200px-Borussia_Dortmund_logo.svg.png', short: 'Dortmund' },
      { name: 'Paris Saint-Germain', logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/a/a7/Paris_Saint-Germain_F.C..svg/200px-Paris_Saint-Germain_F.C..svg.png', short: 'PSG' },
      { name: 'Juventus', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/15/Juventus_FC_2017_logo.svg/200px-Juventus_FC_2017_logo.svg.png', short: 'Juventus' },
      { name: 'AC Milan', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/Logo_of_AC_Milan.svg/200px-Logo_of_AC_Milan.svg.png', short: 'Milan' },
      { name: 'Inter Milan', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/FC_Internazionale_Milano_2021.svg/200px-FC_Internazionale_Milano_2021.svg.png', short: 'Inter' },
      
      // Times argentinos
      { name: 'Boca Juniors', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/41/CABJ_Logo.svg/200px-CABJ_Logo.svg.png', short: 'Boca Juniors' },
      { name: 'River Plate', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ac/Escudo_del_Club_Atl%C3%A9tico_River_Plate.svg/200px-Escudo_del_Club_Atl%C3%A9tico_River_Plate.svg.png', short: 'River Plate' },
      
      // Times uruguaios
      { name: 'Peñarol', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Escudo_del_Club_Atl%C3%A9tico_Pe%C3%B1arol.svg/200px-Escudo_del_Club_Atl%C3%A9tico_Pe%C3%B1arol.svg.png', short: 'Peñarol' },
      { name: 'Nacional', logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/ce/Club_Nacional_de_Football_logo.svg/200px-Club_Nacional_de_Football_logo.svg.png', short: 'Nacional' }
    ]
    
    let updatedCount = 0
    
    for (const team of teamLogos) {
      try {
        const result = await executeQuery(`
          UPDATE times 
          SET logo_url = ?, nome_curto = ?
          WHERE nome LIKE ? OR nome_curto LIKE ? OR nome LIKE ?
        `, [
          team.logo, 
          team.short,
          `%${team.name}%`, 
          `%${team.name}%`,
          `%${team.short}%`
        ])
        
        if (result.affectedRows > 0) {
          console.log(`✅ Logo e nome curto atualizados para ${team.name} -> ${team.short}`)
          updatedCount++
        }
      } catch (error) {
        console.error(`❌ Erro ao atualizar ${team.name}:`, error.message)
      }
    }
    
    console.log(`\n🎨 ${updatedCount} times atualizados com logos e nomes curtos`)
    
    // Verificar quantos times ainda não têm logo
    const [withoutLogo] = await executeQuery(`
      SELECT COUNT(*) as count FROM times 
      WHERE logo_url IS NULL OR logo_url = ''
    `)
    
    const [withLogo] = await executeQuery(`
      SELECT COUNT(*) as count FROM times 
      WHERE logo_url IS NOT NULL AND logo_url != ''
    `)
    
    console.log(`📊 Estatísticas finais:`)
    console.log(`   ✅ Times com logo: ${withLogo.count}`)
    console.log(`   ❌ Times sem logo: ${withoutLogo.count}`)
    
  } catch (error) {
    console.error('❌ Erro ao atualizar logos:', error.message)
  }
}

// Executar
updateAllTeamLogos()
