#!/usr/bin/env node

/**
 * Script para criar dados mock para o admin
 */

import { config } from 'dotenv'
import { initializeDatabase, executeQuery } from '../lib/database-config.js'

// Carregar variáveis de ambiente
config({ path: '.env.local' })

async function createAdminData() {
  try {
    console.log('🚀 Criando dados mock para admin...')
    
    await initializeDatabase()
    
    // Criar usuário admin se não existir
    const adminExists = await executeQuery(
      'SELECT id FROM usuarios WHERE email = ?',
      ['<EMAIL>']
    )
    
    let adminId
    if (adminExists.length === 0) {
      const adminResult = await executeQuery(`
        INSERT INTO usuarios (nome, email, telefone, senha_hash, tipo, status)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        'Administrador Principal',
        '<EMAIL>',
        '(11) 99999-9999',
        '$2b$10$hash_admin123',
        'admin',
        'ativo'
      ])
      adminId = adminResult.lastInsertRowid
      console.log('✅ Usuário admin criado')
    } else {
      adminId = adminExists[0].id
      console.log('✅ Usuário admin já existe')
    }
    
    // Criar alguns cambistas
    const cambistas = [
      {
        nome: 'João Silva',
        email: '<EMAIL>',
        telefone: '(11) 98888-8888',
        endereco: 'Rua das Flores, 123',
        cpf_cnpj: '123.456.789-00'
      },
      {
        nome: 'Maria Santos',
        email: '<EMAIL>',
        telefone: '(11) 97777-7777',
        endereco: 'Av. Principal, 456',
        cpf_cnpj: '987.654.321-00'
      },
      {
        nome: 'Pedro Costa',
        email: '<EMAIL>',
        telefone: '(11) 96666-6666',
        endereco: 'Rua do Comércio, 789',
        cpf_cnpj: '456.789.123-00'
      }
    ]
    
    let cambistasCreated = 0
    for (const cambista of cambistas) {
      const exists = await executeQuery(
        'SELECT id FROM usuarios WHERE email = ?',
        [cambista.email]
      )
      
      if (exists.length === 0) {
        await executeQuery(`
          INSERT INTO usuarios (nome, email, telefone, endereco, cpf_cnpj, senha_hash, tipo, status)
          VALUES (?, ?, ?, ?, ?, ?, 'cambista', 'ativo')
        `, [
          cambista.nome,
          cambista.email,
          cambista.telefone,
          cambista.endereco,
          cambista.cpf_cnpj,
          '$2b$10$hash_cambista123'
        ])
        cambistasCreated++
      }
    }
    console.log(`✅ ${cambistasCreated} cambistas criados`)
    
    // Criar alguns usuários normais
    const usuarios = [
      {
        nome: 'Carlos Oliveira',
        email: '<EMAIL>',
        telefone: '(11) 95555-5555'
      },
      {
        nome: 'Ana Paula',
        email: '<EMAIL>',
        telefone: '(11) 94444-4444'
      },
      {
        nome: 'Roberto Lima',
        email: '<EMAIL>',
        telefone: '(11) 93333-3333'
      },
      {
        nome: 'Fernanda Souza',
        email: '<EMAIL>',
        telefone: '(11) 92222-2222'
      }
    ]
    
    let usuariosCreated = 0
    for (const usuario of usuarios) {
      const exists = await executeQuery(
        'SELECT id FROM usuarios WHERE email = ?',
        [usuario.email]
      )
      
      if (exists.length === 0) {
        await executeQuery(`
          INSERT INTO usuarios (nome, email, telefone, senha_hash, tipo, status)
          VALUES (?, ?, ?, ?, 'usuario', 'ativo')
        `, [
          usuario.nome,
          usuario.email,
          usuario.telefone,
          '$2b$10$hash_usuario123'
        ])
        usuariosCreated++
      }
    }
    console.log(`✅ ${usuariosCreated} usuários criados`)
    
    // Criar alguns bolões
    const boloes = [
      {
        nome: 'Bolão Premier League 2024',
        descricao: 'Apostas na Premier League temporada 2024/25',
        valor_aposta: 25.00,
        premio_total: 1000.00,
        max_participantes: 50,
        min_acertos: 8,
        data_inicio: '2024-08-01',
        data_fim: '2024-12-31',
        status: 'ativo'
      },
      {
        nome: 'Bolão Brasileirão 2024',
        descricao: 'Campeonato Brasileiro Série A 2024',
        valor_aposta: 20.00,
        premio_total: 800.00,
        max_participantes: 40,
        min_acertos: 6,
        data_inicio: '2024-04-01',
        data_fim: '2024-12-15',
        status: 'ativo'
      },
      {
        nome: 'Bolão Champions League',
        descricao: 'Liga dos Campeões da UEFA 2024/25',
        valor_aposta: 30.00,
        premio_total: 1500.00,
        max_participantes: 60,
        min_acertos: 10,
        data_inicio: '2024-09-01',
        data_fim: '2025-05-31',
        status: 'ativo'
      }
    ]
    
    let boloesCreated = 0
    for (const bolao of boloes) {
      const exists = await executeQuery(
        'SELECT id FROM boloes WHERE nome = ?',
        [bolao.nome]
      )
      
      if (exists.length === 0) {
        await executeQuery(`
          INSERT INTO boloes (
            nome, descricao, valor_aposta, premio_total, max_participantes,
            min_acertos, data_inicio, data_fim, status, criado_por
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          bolao.nome,
          bolao.descricao,
          bolao.valor_aposta,
          bolao.premio_total,
          bolao.max_participantes,
          bolao.min_acertos,
          bolao.data_inicio,
          bolao.data_fim,
          bolao.status,
          adminId
        ])
        boloesCreated++
      }
    }
    console.log(`✅ ${boloesCreated} bolões criados`)
    
    // Verificar totais
    const [totalUsuarios] = await executeQuery('SELECT COUNT(*) as total FROM usuarios')
    const [totalBoloes] = await executeQuery('SELECT COUNT(*) as total FROM boloes')
    const [totalCambistas] = await executeQuery("SELECT COUNT(*) as total FROM usuarios WHERE tipo = 'cambista'")
    
    console.log('\n📊 RESUMO FINAL:')
    console.log(`Total de usuários: ${totalUsuarios.total}`)
    console.log(`Total de cambistas: ${totalCambistas.total}`)
    console.log(`Total de bolões: ${totalBoloes.total}`)
    
    return {
      usuarios: usuariosCreated,
      cambistas: cambistasCreated,
      boloes: boloesCreated
    }
    
  } catch (error) {
    console.error('❌ Erro ao criar dados do admin:', error)
    throw error
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  createAdminData().then((result) => {
    console.log(`✅ Processo finalizado! Dados criados: ${JSON.stringify(result)}`)
    process.exit(0)
  }).catch(error => {
    console.error('💥 Processo finalizado com erro:', error.message)
    process.exit(1)
  })
}

export { createAdminData }
