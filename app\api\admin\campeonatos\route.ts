import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET(request: Request) {
  try {
    // Garantir que o banco está inicializado
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const includeStats = searchParams.get("stats") !== "false"
    const includeJogos = searchParams.get("jogos") === "true"

    // Buscar campeonatos com informações básicas (sem estatísticas complexas para evitar erro SQL)
    const campeonatos = await executeQuery(`
      SELECT
        c.*,
        0 as total_jogos,
        0 as jogos_futuros,
        0 as jogos_finalizados,
        0 as jogos_hoje,
        0 as total_times
      FROM campeonatos c
      ORDER BY c.status DESC, c.data_criacao DESC
    `)

    let stats = null
    if (includeStats) {
      const statsData = await executeQuery(`
        SELECT
          COUNT(DISTINCT c.id) as total_campeonatos,
          COUNT(DISTINCT CASE WHEN c.status = 'ativo' THEN c.id END) as ativos,
          COUNT(DISTINCT CASE WHEN c.pais != 'Brasil' THEN c.id END) as internacionais
        FROM campeonatos c
      `)

      const jogosStats = await executeQuery(`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN j.status = 'agendado' AND j.data_jogo >= NOW() THEN 1 END) as agendados,
          COUNT(CASE WHEN j.status = 'ao_vivo' THEN 1 END) as ao_vivo,
          COUNT(CASE WHEN j.status = 'finalizado' THEN 1 END) as finalizados,
          COUNT(CASE WHEN DATE(j.data_jogo) = CURDATE() THEN 1 END) as hoje
        FROM jogos j
        LEFT JOIN campeonatos c ON j.campeonato_id = c.id
        WHERE c.status = 'ativo'
      `)

      const baseStats = statsData[0] || { total_campeonatos: 0, ativos: 0, internacionais: 0 }
      const gameStats = jogosStats[0] || { total: 0, agendados: 0, ao_vivo: 0, finalizados: 0, hoje: 0 }

      stats = {
        ...baseStats,
        total_jogos: gameStats.total,
        jogos_hoje: gameStats.hoje,
        jogos_futuros: gameStats.agendados
      }
    }

    // Se solicitado, incluir próximos jogos para cada campeonato
    let campeonatosComJogos = campeonatos
    if (includeJogos) {
      campeonatosComJogos = await Promise.all(
        campeonatos.map(async (campeonato) => {
          const proximosJogos = await executeQuery(`
            SELECT
              j.*,
              c.nome as campeonato_nome,
              tc.nome as time_casa_nome,
              tf.nome as time_fora_nome
            FROM jogos j
            LEFT JOIN campeonatos c ON j.campeonato_id = c.id
            LEFT JOIN times tc ON j.time_casa_id = tc.id
            LEFT JOIN times tf ON j.time_fora_id = tf.id
            WHERE c.id = ? AND j.status = 'agendado' AND j.data_jogo >= NOW()
            ORDER BY j.data_jogo ASC
            LIMIT 5
          `, [campeonato.id])

          return {
            ...campeonato,
            proximos_jogos: proximosJogos || []
          }
        })
      )
    }

    return NextResponse.json({
      success: true,
      campeonatos: campeonatosComJogos || [],
      stats: stats,
      total: campeonatos?.length || 0
    })
  } catch (error) {
    console.error("Erro ao buscar campeonatos:", error)

    // Sempre retornar JSON válido, mesmo em caso de erro
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        campeonatos: [],
        stats: null,
        total: 0
      },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  }
}
