import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await initializeDatabase()

    const bolaoId = parseInt(params.id)
    const data = await request.json()

    // Verificar se o bolão existe
    const bolao = await executeQuerySingle('SELECT id FROM boloes WHERE id = ?', [bolaoId])
    if (!bolao) {
      return NextResponse.json(
        {
          success: false,
          error: "Bolão não encontrado",
        },
        { status: 404 }
      )
    }

    // Atualizar status do bolão
    if (data.status) {
      await executeQuery('UPDATE boloes SET status = ? WHERE id = ?', [data.status, bolaoId])
      
      return NextResponse.json({
        success: true,
        message: `Bolão ${data.status === 'ativo' ? 'ativado' : 'desativado'} com sucesso`,
      })
    }

    return NextResponse.json(
      {
        success: false,
        error: "Dados inválidos para atualização",
      },
      { status: 400 }
    )
  } catch (error) {
    console.error("Erro ao atualizar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await initializeDatabase()

    const bolaoId = parseInt(params.id)

    // Verificar se o bolão existe
    const bolao = await executeQuerySingle('SELECT id, nome FROM boloes WHERE id = ?', [bolaoId])
    if (!bolao) {
      return NextResponse.json(
        {
          success: false,
          error: "Bolão não encontrado",
        },
        { status: 404 }
      )
    }

    // Verificar se há apostas associadas
    const apostas = await executeQuerySingle('SELECT COUNT(*) as total FROM apostas WHERE bolao_id = ?', [bolaoId])
    if (apostas && apostas.total > 0) {
      return NextResponse.json(
        {
          success: false,
          error: "Não é possível deletar um bolão que já possui apostas",
        },
        { status: 400 }
      )
    }

    // Deletar o bolão
    await executeQuery('DELETE FROM boloes WHERE id = ?', [bolaoId])

    return NextResponse.json({
      success: true,
      message: "Bolão deletado com sucesso",
    })
  } catch (error) {
    console.error("Erro ao deletar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    await initializeDatabase()

    const bolaoId = parseInt(params.id)
    const data = await request.json()

    // Verificar se o bolão existe
    const bolao = await executeQuerySingle('SELECT id FROM boloes WHERE id = ?', [bolaoId])
    if (!bolao) {
      return NextResponse.json(
        {
          success: false,
          error: "Bolão não encontrado",
        },
        { status: 404 }
      )
    }

    // Atualizar dados do bolão
    await executeQuery(`
      UPDATE boloes SET
        nome = ?,
        descricao = ?,
        valor_aposta = ?,
        premio_total = ?,
        max_participantes = ?,
        data_inicio = ?,
        data_fim = ?,
        campeonatos_selecionados = ?,
        partidas_selecionadas = ?
      WHERE id = ?
    `, [
      data.nome,
      data.descricao,
      data.valor_aposta,
      data.premio_total,
      data.max_participantes,
      data.data_inicio,
      data.data_fim,
      JSON.stringify(data.campeonatos_selecionados),
      JSON.stringify(data.partidas_selecionadas),
      bolaoId
    ])

    return NextResponse.json({
      success: true,
      message: "Bolão atualizado com sucesso",
    })
  } catch (error) {
    console.error("Erro ao atualizar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}
