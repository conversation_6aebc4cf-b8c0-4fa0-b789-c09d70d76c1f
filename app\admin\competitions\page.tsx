"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Trophy, Plus, Search, Calendar, Globe, Loader2, CheckCircle } from "lucide-react"

interface Competition {
  id: number
  name: string
  code: string
  type: string
  area: {
    id: number
    name: string
    code: string
    flag: string
  }
  emblem: string
  currentSeason?: {
    id: number
    startDate: string
    endDate: string
    currentMatchday: number
  }
}

interface Match {
  id: number
  competition: {
    name: string
    emblem: string
  }
  utcDate: string
  homeTeam: {
    id: number
    name: string
    crest: string
  }
  awayTeam: {
    id: number
    name: string
    crest: string
  }
  status: string
}

interface NewBolao {
  name: string
  description: string
  value: number
  selectedCompetitions: number[]
  selectedMatches: number[]
}

export default function CompetitionsAdminPage() {
  const [competitions, setCompetitions] = useState<Competition[]>([])
  const [matches, setMatches] = useState<Match[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMatches, setLoadingMatches] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null)
  const [newBolao, setNewBolao] = useState<NewBolao>({
    name: "",
    description: "",
    value: 25,
    selectedCompetitions: [],
    selectedMatches: [],
  })

  useEffect(() => {
    loadCompetitions()
  }, [])

  const loadCompetitions = async () => {
    try {
      setLoading(true)

      // Simular dados da API Football-Data.org
      // Em produção: const response = await fetch('https://api.football-data.org/v4/competitions', { headers: { 'X-Auth-Token': 'YOUR_API_KEY' } })

      const mockCompetitions: Competition[] = [
        {
          id: 2013,
          name: "Campeonato Brasileiro Série A",
          code: "BSA",
          type: "LEAGUE",
          area: {
            id: 76,
            name: "Brazil",
            code: "BRA",
            flag: "https://crests.football-data.org/76.svg",
          },
          emblem: "https://crests.football-data.org/BSA.png",
          currentSeason: {
            id: 1,
            startDate: "2024-04-13",
            endDate: "2024-12-08",
            currentMatchday: 15,
          },
        },
        {
          id: 2014,
          name: "Copa do Brasil",
          code: "CDB",
          type: "CUP",
          area: {
            id: 76,
            name: "Brazil",
            code: "BRA",
            flag: "https://crests.football-data.org/76.svg",
          },
          emblem: "https://crests.football-data.org/CDB.png",
          currentSeason: {
            id: 2,
            startDate: "2024-02-28",
            endDate: "2024-11-10",
            currentMatchday: 8,
          },
        },
        {
          id: 2152,
          name: "Copa Libertadores",
          code: "CLI",
          type: "CUP",
          area: {
            id: 2001,
            name: "South America",
            code: "SAM",
            flag: "https://crests.football-data.org/CLI.svg",
          },
          emblem: "https://crests.football-data.org/CLI.png",
          currentSeason: {
            id: 3,
            startDate: "2024-02-06",
            endDate: "2024-11-30",
            currentMatchday: 6,
          },
        },
        {
          id: 2000,
          name: "FIFA World Cup",
          code: "WC",
          type: "CUP",
          area: {
            id: 2267,
            name: "World",
            code: "INT",
            flag: "https://crests.football-data.org/qatar.svg",
          },
          emblem: "https://crests.football-data.org/WC.svg",
        },
        {
          id: 2001,
          name: "UEFA Champions League",
          code: "CL",
          type: "CUP",
          area: {
            id: 2077,
            name: "Europe",
            code: "EUR",
            flag: "https://crests.football-data.org/EUR.svg",
          },
          emblem: "https://crests.football-data.org/CL.png",
        },
        {
          id: 2021,
          name: "Premier League",
          code: "PL",
          type: "LEAGUE",
          area: {
            id: 2072,
            name: "England",
            code: "ENG",
            flag: "https://crests.football-data.org/770.svg",
          },
          emblem: "https://crests.football-data.org/PL.png",
        },
      ]

      await new Promise((resolve) => setTimeout(resolve, 1000))
      setCompetitions(mockCompetitions)
    } catch (error) {
      console.error("Erro ao carregar competições:", error)
    } finally {
      setLoading(false)
    }
  }

  const loadMatches = async (competitionId: number) => {
    try {
      setLoadingMatches(true)

      // Simular dados da API
      const mockMatches: Match[] = [
        {
          id: 1,
          competition: {
            name: "Brasileirão Série A",
            emblem: "https://crests.football-data.org/BSA.png",
          },
          utcDate: "2024-01-20T19:00:00Z",
          homeTeam: {
            id: 1,
            name: "Flamengo",
            crest: "https://crests.football-data.org/1.png",
          },
          awayTeam: {
            id: 2,
            name: "Palmeiras",
            crest: "https://crests.football-data.org/2.png",
          },
          status: "SCHEDULED",
        },
        {
          id: 2,
          competition: {
            name: "Brasileirão Série A",
            emblem: "https://crests.football-data.org/BSA.png",
          },
          utcDate: "2024-01-21T21:30:00Z",
          homeTeam: {
            id: 3,
            name: "São Paulo",
            crest: "https://crests.football-data.org/3.png",
          },
          awayTeam: {
            id: 4,
            name: "Corinthians",
            crest: "https://crests.football-data.org/4.png",
          },
          status: "SCHEDULED",
        },
      ]

      await new Promise((resolve) => setTimeout(resolve, 800))
      setMatches(mockMatches)
    } catch (error) {
      console.error("Erro ao carregar partidas:", error)
    } finally {
      setLoadingMatches(false)
    }
  }

  const handleCompetitionSelect = (competition: Competition) => {
    setSelectedCompetition(competition)
    loadMatches(competition.id)

    // Adicionar à seleção do bolão
    if (!newBolao.selectedCompetitions.includes(competition.id)) {
      setNewBolao((prev) => ({
        ...prev,
        selectedCompetitions: [...prev.selectedCompetitions, competition.id],
      }))
    }
  }

  const handleMatchSelect = (matchId: number) => {
    setNewBolao((prev) => ({
      ...prev,
      selectedMatches: prev.selectedMatches.includes(matchId)
        ? prev.selectedMatches.filter((id) => id !== matchId)
        : [...prev.selectedMatches, matchId],
    }))
  }

  const createBolao = async () => {
    try {
      if (!newBolao.name || newBolao.selectedMatches.length < 3) {
        alert("Preencha o nome e selecione pelo menos 3 partidas")
        return
      }

      // Simular criação do bolão
      await new Promise((resolve) => setTimeout(resolve, 1000))

      alert(
        `Bolão "${newBolao.name}" criado com sucesso!\n\nPartidas selecionadas: ${newBolao.selectedMatches.length}\nValor: R$ ${newBolao.value}`,
      )

      // Reset form
      setNewBolao({
        name: "",
        description: "",
        value: 25,
        selectedCompetitions: [],
        selectedMatches: [],
      })
    } catch (error) {
      console.error("Erro ao criar bolão:", error)
    }
  }

  const filteredCompetitions = competitions.filter(
    (comp) =>
      comp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comp.area.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-green-600 mx-auto mb-4" />
          <p className="text-gray-600 text-lg">Carregando competições da API...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: "#2D3748" }}>
      {/* Header */}
      <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-gray-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Trophy className="h-8 w-8 text-green-600" />
              <h1 className="text-2xl font-bold text-gray-900">Gerenciar Competições</h1>
            </div>
            <Badge variant="secondary">Admin</Badge>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="competitions" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="competitions">Competições Disponíveis</TabsTrigger>
            <TabsTrigger value="create-bolao">Criar Novo Bolão</TabsTrigger>
          </TabsList>

          {/* Tab Competições */}
          <TabsContent value="competitions">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="h-6 w-6 mr-2" />
                  Competições da API Football-Data.org
                </CardTitle>
                <CardDescription>Explore competições disponíveis e suas partidas para criar bolões</CardDescription>

                {/* Busca */}
                <div className="flex items-center space-x-2 mt-4">
                  <Search className="h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Buscar competições..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredCompetitions.map((competition) => (
                    <div
                      key={competition.id}
                      className="bg-white/95 backdrop-blur-sm border border-gray-300 rounded-lg p-4 hover:shadow-xl transition-all duration-300 cursor-pointer"
                      onClick={() => handleCompetitionSelect(competition)}
                    >
                      <div className="flex items-center space-x-3 mb-3">
                        <img
                          src={competition.emblem || "/placeholder.svg?height=40&width=40"}
                          alt={competition.name}
                          className="w-10 h-10"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/placeholder.svg?height=40&width=40"
                          }}
                        />
                        <div className="flex-1">
                          <h3 className="font-semibold text-sm">{competition.name}</h3>
                          <p className="text-xs text-gray-600">{competition.code}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 mb-2">
                        <img
                          src={competition.area.flag || "/placeholder.svg?height=20&width=20"}
                          alt={competition.area.name}
                          className="w-5 h-5"
                        />
                        <span className="text-sm text-gray-600">{competition.area.name}</span>
                      </div>

                      <Badge variant={competition.type === "LEAGUE" ? "default" : "secondary"} className="text-xs">
                        {competition.type}
                      </Badge>

                      {competition.currentSeason && (
                        <div className="mt-2 text-xs text-gray-500">
                          <p>Rodada: {competition.currentSeason.currentMatchday}</p>
                          <p>
                            {formatDate(competition.currentSeason.startDate)} -{" "}
                            {formatDate(competition.currentSeason.endDate)}
                          </p>
                        </div>
                      )}

                      {newBolao.selectedCompetitions.includes(competition.id) && (
                        <div className="mt-2">
                          <Badge className="bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Selecionada
                          </Badge>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Partidas da Competição Selecionada */}
                {selectedCompetition && (
                  <div className="mt-8 border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center">
                      <Calendar className="h-5 w-5 mr-2" />
                      Partidas - {selectedCompetition.name}
                    </h3>

                    {loadingMatches ? (
                      <div className="text-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-green-600 mx-auto mb-2" />
                        <p className="text-gray-600">Carregando partidas...</p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {matches.map((match) => (
                          <div
                            key={match.id}
                            className={`border rounded-lg p-4 cursor-pointer transition-all ${
                              newBolao.selectedMatches.includes(match.id)
                                ? "border-green-500 bg-green-50"
                                : "hover:shadow-md"
                            }`}
                            onClick={() => handleMatchSelect(match.id)}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600">{formatDate(match.utcDate)}</span>
                              {newBolao.selectedMatches.includes(match.id) && (
                                <CheckCircle className="h-5 w-5 text-green-600" />
                              )}
                            </div>

                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <img
                                  src={match.homeTeam.crest || "/placeholder.svg?height=24&width=24"}
                                  alt={match.homeTeam.name}
                                  className="w-6 h-6"
                                />
                                <span className="text-sm font-medium">{match.homeTeam.name}</span>
                              </div>

                              <span className="text-xs text-gray-500">VS</span>

                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium">{match.awayTeam.name}</span>
                                <img
                                  src={match.awayTeam.crest || "/placeholder.svg?height=24&width=24"}
                                  alt={match.awayTeam.name}
                                  className="w-6 h-6"
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Criar Bolão */}
          <TabsContent value="create-bolao">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Plus className="h-6 w-6 mr-2" />
                  Criar Novo Bolão
                </CardTitle>
                <CardDescription>Configure um novo bolão com as partidas selecionadas</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="bolao-name">Nome do Bolão</Label>
                      <Input
                        id="bolao-name"
                        placeholder="Ex: Super Bolão Brasileirão 2024"
                        value={newBolao.name}
                        onChange={(e) => setNewBolao((prev) => ({ ...prev, name: e.target.value }))}
                      />
                    </div>

                    <div>
                      <Label htmlFor="bolao-description">Descrição</Label>
                      <Input
                        id="bolao-description"
                        placeholder="Descrição do bolão..."
                        value={newBolao.description}
                        onChange={(e) => setNewBolao((prev) => ({ ...prev, description: e.target.value }))}
                      />
                    </div>

                    <div>
                      <Label htmlFor="bolao-value">Valor da Aposta (R$)</Label>
                      <Input
                        id="bolao-value"
                        type="number"
                        min="1"
                        value={newBolao.value}
                        onChange={(e) => setNewBolao((prev) => ({ ...prev, value: Number(e.target.value) }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label>Competições Selecionadas</Label>
                      <div className="text-sm text-gray-600">
                        {newBolao.selectedCompetitions.length} competição(ões) selecionada(s)
                      </div>
                    </div>

                    <div>
                      <Label>Partidas Selecionadas</Label>
                      <div className="text-sm text-gray-600">
                        {newBolao.selectedMatches.length} partida(s) selecionada(s)
                      </div>
                      {newBolao.selectedMatches.length < 3 && (
                        <p className="text-sm text-red-600 mt-1">Mínimo 3 partidas necessárias</p>
                      )}
                    </div>

                    <div className="pt-4">
                      <Button
                        onClick={createBolao}
                        disabled={!newBolao.name || newBolao.selectedMatches.length < 3}
                        className="w-full bg-green-600 hover:bg-green-700"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Criar Bolão
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
