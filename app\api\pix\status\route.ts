import { NextRequest, NextResponse } from "next/server"
import { createConnection } from "@/lib/db"

const PIX_API_BASE_URL = 'https://api.meiodepagamento.com/api/V1/Transacao'
const PIX_TOKEN = 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { transaction_id } = body

    if (!transaction_id) {
      return NextResponse.json({ error: "transaction_id é obrigatório" }, { status: 400 })
    }

    console.log("🔍 Verificando status do pagamento PIX:", transaction_id)

    // Consultar status na API PIX
    const response = await fetch(`${PIX_API_BASE_URL}/ConsultarTransacao`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: PIX_TOKEN,
        transaction_id: transaction_id
      })
    })

    if (!response.ok) {
      console.error("❌ Erro na API PIX:", response.status)
      return NextResponse.json(
        { error: "Erro ao consultar status do pagamento" },
        { status: response.status }
      )
    }

    const pixResponse = await response.json()
    console.log("📊 Status PIX recebido:", pixResponse)

    // Se o pagamento foi aprovado, atualizar no banco
    if (pixResponse.status === 'approved' || pixResponse.status === 'paid') {
      const connection = await createConnection()

      try {
        // Atualizar status do bilhete
        await connection.execute(`
          UPDATE bilhetes 
          SET status = 'pago' 
          WHERE transaction_id = ?
        `, [transaction_id])

        // Atualizar status do pagamento
        await connection.execute(`
          UPDATE pagamentos 
          SET status = 'aprovado' 
          WHERE transaction_id = ?
        `, [transaction_id])

        console.log("✅ Status atualizado no banco para:", transaction_id)

      } catch (dbError) {
        console.error("❌ Erro ao atualizar banco:", dbError)
      } finally {
        await connection.end()
      }
    }

    return NextResponse.json({
      success: true,
      status: pixResponse.status,
      transaction_id: transaction_id,
      paid: pixResponse.status === 'approved' || pixResponse.status === 'paid'
    })

  } catch (error) {
    console.error("❌ Erro ao verificar status PIX:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
