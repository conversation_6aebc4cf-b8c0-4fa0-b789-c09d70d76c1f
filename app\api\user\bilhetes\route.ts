import { NextRequest, NextResponse } from "next/server"
import { createConnection } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json({ error: "user_id é obrigatório" }, { status: 400 })
    }

    const connection = await createConnection()

    // Buscar bilhetes do usuário
    const [bilhetes] = await connection.execute(`
      SELECT 
        b.id,
        b.codigo,
        b.valor,
        b.status,
        b.premio,
        b.created_at,
        b.qr_code_pix,
        b.transaction_id,
        GROUP_CONCAT(
          CONCAT(
            m.home_team, ' x ', m.away_team, '|',
            ba.resultado
          ) SEPARATOR ';'
        ) as apostas_data
      FROM bilhetes b
      LEFT JOIN bilhete_apostas ba ON b.id = ba.bilhete_id
      LEFT JOIN matches m ON ba.match_id = m.id
      WHERE b.user_id = ?
      GROUP BY b.id
      ORDER BY b.created_at DESC
    `, [userId])

    // Formatar os dados para o frontend
    const bilhetesFormatados = (bilhetes as any[]).map(bilhete => {
      const apostas = bilhete.apostas_data 
        ? bilhete.apostas_data.split(';').map((apostaStr: string) => {
            const [jogo, resultado] = apostaStr.split('|')
            return { jogo, resultado }
          })
        : []

      return {
        id: bilhete.codigo,
        data: new Date(bilhete.created_at).toLocaleDateString('pt-BR'),
        hora: new Date(bilhete.created_at).toLocaleTimeString('pt-BR', { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        apostas,
        valor: parseFloat(bilhete.valor),
        status: bilhete.status,
        premio: bilhete.premio ? parseFloat(bilhete.premio) : 0,
        qr_code_pix: bilhete.qr_code_pix,
        transaction_id: bilhete.transaction_id
      }
    })

    await connection.end()

    return NextResponse.json({
      success: true,
      bilhetes: bilhetesFormatados
    })

  } catch (error) {
    console.error("❌ Erro ao buscar bilhetes:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
