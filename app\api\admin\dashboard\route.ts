import { NextResponse } from "next/server"
import { getDashboardStats, initializeDatabase } from "@/lib/database-config"

export async function GET() {
  try {
    await initializeDatabase()

    const stats = await getDashboardStats()

    return NextResponse.json(
      {
        success: true,
        stats: stats || {
          totalBoloes: 0,
          totalUsuarios: 0,
          faturamentoMes: 0,
          apostasHoje: 0,
          cambistasAtivos: 0,
          jogosHoje: 0,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  } catch (error) {
    console.error("Erro ao buscar stats do dashboard:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        stats: {
          totalBoloes: 0,
          totalUsuarios: 0,
          faturamentoMes: 0,
          apostasHoje: 0,
          cambistasAtivos: 0,
          jogosHoje: 0,
        },
      },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  }
}
