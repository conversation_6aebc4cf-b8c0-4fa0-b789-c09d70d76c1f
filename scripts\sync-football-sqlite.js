#!/usr/bin/env node

import { config } from 'dotenv'
import { initializeDatabase, executeQuery, executeQuerySingle } from '../lib/database-sqlite.js'
import { FootballAPI } from '../lib/football-api.js'

// Carregar variáveis de ambiente
config({ path: '.env.local' })

const footballAPI = new FootballAPI()

// Configurações de sincronização
const SYNC_CONFIG = {
  maxRetries: 3,
  retryDelay: 2000, // 2 segundos
  requestDelay: 300, // 300ms entre requisições para respeitar rate limit
  batchSize: 10, // Processar em lotes
  maxConcurrent: 3 // Máximo de requisições simultâneas
}

// Todas as competições que queremos sincronizar (sem filtro - pegar TODAS)
const PRIORITY_COMPETITIONS = [
  'PL', 'PD', 'SA', 'BL1', 'FL1', // Top 5 Europeus
  'CL', 'EL', 'ECL', // UEFA
  'WC', 'EC', // Internacionais
  'BSA', 'CLI', 'CSA', // América do Sul
  'DED', 'PPL', 'ELC' // Outros importantes
]

// Função para delay entre requisições
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// Função para retry com backoff exponencial
async function retryWithBackoff(fn, maxRetries = SYNC_CONFIG.maxRetries) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      if (i === maxRetries - 1) throw error

      const delayTime = SYNC_CONFIG.retryDelay * Math.pow(2, i)
      console.log(`⚠️ Tentativa ${i + 1} falhou, tentando novamente em ${delayTime}ms...`)
      await delay(delayTime)
    }
  }
}

// Função para log de sincronização
async function logSync(type, action, details = {}) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    type,
    action,
    details: JSON.stringify(details)
  }

  try {
    await executeQuery(`
      INSERT INTO sync_logs (timestamp, type, action, details)
      VALUES (?, ?, ?, ?)
    `, [logEntry.timestamp, logEntry.type, logEntry.action, logEntry.details])
  } catch (error) {
    console.error('Erro ao salvar log:', error)
  }
}

async function syncCompetitions() {
  try {
    console.log('🏆 Sincronizando TODAS as competições...')

    // Buscar todas as competições da API
    const competitionsData = await retryWithBackoff(() => footballAPI.getCompetitions())

    if (!competitionsData.competitions) {
      throw new Error('Nenhuma competição encontrada na API')
    }

    console.log(`📋 Encontradas ${competitionsData.competitions.length} competições na API`)
    await logSync('competitions', 'start', { total: competitionsData.competitions.length })

    let syncedCount = 0
    let errorCount = 0

    for (const competition of competitionsData.competitions) {
      try {
        // Sincronizar TODAS as competições, não apenas as principais
        console.log(`🔄 Processando: ${competition.name}...`)

        // Verificar se já existe no banco
        const existing = await executeQuerySingle(
          'SELECT id FROM campeonatos WHERE api_id = ?',
          [competition.id.toString()]
        )

        const competitionData = {
          nome: competition.name,
          descricao: `${competition.name} - ${competition.area?.name || 'Internacional'}`,
          pais: competition.area?.name || 'Internacional',
          temporada: getCurrentSeason(),
          status: 'ativo',
          data_inicio: competition.currentSeason?.startDate || null,
          data_fim: competition.currentSeason?.endDate || null,
          api_id: competition.id.toString(),
          logo_url: competition.emblem || null,
          codigo: competition.code || null,
          tipo: competition.type || 'LEAGUE',
          plano: competition.plan || 'TIER_FOUR'
        }

        if (existing) {
          // Atualizar existente
          await executeQuery(`
            UPDATE campeonatos SET
              nome = ?, descricao = ?, pais = ?, temporada = ?,
              data_inicio = ?, data_fim = ?, logo_url = ?, codigo = ?, tipo = ?, plano = ?
            WHERE api_id = ?
          `, [
            competitionData.nome,
            competitionData.descricao,
            competitionData.pais,
            competitionData.temporada,
            competitionData.data_inicio,
            competitionData.data_fim,
            competitionData.logo_url,
            competitionData.codigo,
            competitionData.tipo,
            competitionData.plano,
            competitionData.api_id
          ])
          console.log(`✅ Atualizado: ${competition.name}`)
        } else {
          // Inserir novo
          await executeQuery(`
            INSERT INTO campeonatos (nome, descricao, pais, temporada, status, data_inicio, data_fim, api_id, logo_url, codigo, tipo, plano)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            competitionData.nome,
            competitionData.descricao,
            competitionData.pais,
            competitionData.temporada,
            competitionData.status,
            competitionData.data_inicio,
            competitionData.data_fim,
            competitionData.api_id,
            competitionData.logo_url,
            competitionData.codigo,
            competitionData.tipo,
            competitionData.plano
          ])
          console.log(`➕ Adicionado: ${competition.name}`)
        }
        
        syncedCount++

        // Delay para não sobrecarregar a API
        await delay(SYNC_CONFIG.requestDelay)

      } catch (error) {
        console.error(`❌ Erro ao sincronizar ${competition.name}:`, error.message)
        errorCount++
        await logSync('competitions', 'error', {
          competition: competition.name,
          error: error.message
        })
      }
    }

    console.log(`✅ Sincronizados ${syncedCount} campeonatos (${errorCount} erros)`)
    await logSync('competitions', 'complete', {
      synced: syncedCount,
      errors: errorCount,
      total: competitionsData.competitions.length
    })

    return syncedCount

  } catch (error) {
    console.error('❌ Erro ao sincronizar campeonatos:', error)
    await logSync('competitions', 'failed', { error: error.message })
    throw error
  }
}

async function syncTeamsForCompetition(competitionApiId, competitionId, competitionName) {
  try {
    console.log(`👥 Sincronizando times da competição ${competitionName} (${competitionApiId})...`)

    const teamsData = await retryWithBackoff(() =>
      footballAPI.getCompetitionTeams(competitionApiId, getCurrentSeason())
    )

    if (!teamsData.teams) {
      console.log(`⚠️ Nenhum time encontrado para competição ${competitionName}`)
      return 0
    }

    console.log(`📋 Encontrados ${teamsData.teams.length} times para ${competitionName}`)
    let syncedCount = 0
    let errorCount = 0

    for (const team of teamsData.teams) {
      try {
        // Verificar se já existe no banco
        const existing = await executeQuerySingle(
          'SELECT id FROM times WHERE api_id = ?',
          [team.id.toString()]
        )

        const teamData = {
          nome: team.name,
          nome_curto: team.shortName || team.tla || team.name.substring(0, 3).toUpperCase(),
          cidade: team.venue || null,
          estado: null,
          pais: team.area?.name || null,
          logo_url: team.crest || null,
          api_id: team.id.toString(),
          fundacao: team.founded || null,
          website: team.website || null,
          cores: team.clubColors || null
        }

        if (existing) {
          // Atualizar existente
          await executeQuery(`
            UPDATE times SET
              nome = ?, nome_curto = ?, cidade = ?, pais = ?, logo_url = ?,
              fundacao = ?, website = ?, cores = ?, data_atualizacao = CURRENT_TIMESTAMP
            WHERE api_id = ?
          `, [
            teamData.nome,
            teamData.nome_curto,
            teamData.cidade,
            teamData.pais,
            teamData.logo_url,
            teamData.fundacao,
            teamData.website,
            teamData.cores,
            teamData.api_id
          ])
        } else {
          // Inserir novo
          await executeQuery(`
            INSERT INTO times (nome, nome_curto, cidade, estado, pais, logo_url, api_id, fundacao, website, cores)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            teamData.nome,
            teamData.nome_curto,
            teamData.cidade,
            teamData.estado,
            teamData.pais,
            teamData.logo_url,
            teamData.api_id,
            teamData.fundacao,
            teamData.website,
            teamData.cores
          ])
        }

        syncedCount++
        await delay(SYNC_CONFIG.requestDelay / 2) // Delay menor para times

      } catch (error) {
        console.error(`❌ Erro ao sincronizar time ${team.name}:`, error.message)
        errorCount++
      }
    }

    console.log(`✅ Sincronizados ${syncedCount} times para ${competitionName} (${errorCount} erros)`)
    await logSync('teams', 'complete', {
      competition: competitionName,
      synced: syncedCount,
      errors: errorCount,
      total: teamsData.teams.length
    })

    return syncedCount

  } catch (error) {
    console.error(`❌ Erro ao sincronizar times da competição ${competitionName}:`, error)
    await logSync('teams', 'failed', {
      competition: competitionName,
      error: error.message
    })
    return 0
  }
}

function getCurrentSeason() {
  const now = new Date()
  const year = now.getFullYear()
  
  // Se estamos no primeiro semestre, a temporada atual é do ano anterior
  if (now.getMonth() < 6) {
    return (year - 1).toString()
  }
  
  return year.toString()
}

async function syncMatches() {
  try {
    console.log('⚽ Sincronizando partidas...')

    // Buscar competições ativas do banco
    const competitions = await executeQuery(`
      SELECT id, api_id, nome, codigo FROM campeonatos
      WHERE status = 'ativo' AND api_id IS NOT NULL
      ORDER BY
        CASE WHEN codigo IN ('PL', 'PD', 'SA', 'BL1', 'FL1', 'CL', 'EL') THEN 0 ELSE 1 END,
        nome
    `)

    if (competitions.length === 0) {
      console.log('⚠️ Nenhuma competição ativa encontrada')
      return 0
    }

    console.log(`📋 Processando ${competitions.length} competições...`)
    let totalMatches = 0
    let totalErrors = 0

    for (const competition of competitions) {
      try {
        console.log(`🔄 Processando partidas de ${competition.nome}...`)

        // Buscar partidas de diferentes períodos
        const periods = [
          { name: 'Próximas 2 semanas', days: 14, status: 'SCHEDULED' },
          { name: 'Últimas 2 semanas', days: -14, status: 'FINISHED' },
          { name: 'Ao vivo', days: 0, status: 'LIVE' }
        ]

        let competitionMatches = 0

        for (const period of periods) {
          try {
            const dateFrom = new Date()
            const dateTo = new Date()

            if (period.days > 0) {
              dateTo.setDate(dateTo.getDate() + period.days)
            } else if (period.days < 0) {
              dateFrom.setDate(dateFrom.getDate() + period.days)
              dateTo.setDate(dateTo.getDate())
            } else {
              // Para jogos ao vivo, usar apenas hoje
              dateFrom.setDate(dateFrom.getDate())
              dateTo.setDate(dateTo.getDate())
            }

            const matchesData = await retryWithBackoff(() =>
              footballAPI.getCompetitionMatches(competition.api_id, {
                dateFrom: dateFrom.toISOString().split('T')[0],
                dateTo: dateTo.toISOString().split('T')[0],
                status: period.status
              })
            )

            if (!matchesData.matches || matchesData.matches.length === 0) {
              console.log(`⚠️ Nenhuma partida ${period.name.toLowerCase()} para ${competition.nome}`)
              continue
            }

            console.log(`📅 ${period.name}: ${matchesData.matches.length} partidas`)
            let periodMatches = 0

            for (const match of matchesData.matches) {
              try {
                // Buscar times no banco
                const homeTeam = await executeQuerySingle(
                  'SELECT id FROM times WHERE api_id = ?',
                  [match.homeTeam.id.toString()]
                )

                const awayTeam = await executeQuerySingle(
                  'SELECT id FROM times WHERE api_id = ?',
                  [match.awayTeam.id.toString()]
                )

                if (!homeTeam || !awayTeam) {
                  console.log(`⚠️ Times não encontrados para partida ${match.id}`)
                  continue
                }

                // Verificar se já existe
                const existing = await executeQuerySingle(
                  'SELECT id FROM jogos WHERE api_id = ?',
                  [match.id.toString()]
                )

                // Mapear status da API para nosso sistema
                const statusMap = {
                  'SCHEDULED': 'agendado',
                  'LIVE': 'ao_vivo',
                  'IN_PLAY': 'ao_vivo',
                  'PAUSED': 'ao_vivo',
                  'FINISHED': 'finalizado',
                  'POSTPONED': 'adiado',
                  'SUSPENDED': 'suspenso',
                  'CANCELLED': 'cancelado'
                }

                const matchData = {
                  campeonato_id: competition.id,
                  time_casa_id: homeTeam.id,
                  time_fora_id: awayTeam.id,
                  data_jogo: new Date(match.utcDate).toISOString(),
                  local_jogo: match.venue || null,
                  rodada: match.matchday || null,
                  resultado_casa: match.score?.fullTime?.home || null,
                  resultado_fora: match.score?.fullTime?.away || null,
                  status: statusMap[match.status] || 'agendado',
                  api_id: match.id.toString(),
                  grupo: match.group || null,
                  fase: match.stage || null
                }

                if (existing) {
                  // Atualizar existente
                  await executeQuery(`
                    UPDATE jogos SET
                      data_jogo = ?, local_jogo = ?, rodada = ?, resultado_casa = ?, resultado_fora = ?,
                      status = ?, grupo = ?, fase = ?, data_atualizacao = CURRENT_TIMESTAMP
                    WHERE api_id = ?
                  `, [
                    matchData.data_jogo,
                    matchData.local_jogo,
                    matchData.rodada,
                    matchData.resultado_casa,
                    matchData.resultado_fora,
                    matchData.status,
                    matchData.grupo,
                    matchData.fase,
                    matchData.api_id
                  ])
                } else {
                  // Inserir novo
                  await executeQuery(`
                    INSERT INTO jogos (campeonato_id, time_casa_id, time_fora_id, data_jogo, local_jogo, rodada,
                                     resultado_casa, resultado_fora, status, api_id, grupo, fase)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                  `, [
                    matchData.campeonato_id,
                    matchData.time_casa_id,
                    matchData.time_fora_id,
                    matchData.data_jogo,
                    matchData.local_jogo,
                    matchData.rodada,
                    matchData.resultado_casa,
                    matchData.resultado_fora,
                    matchData.status,
                    matchData.api_id,
                    matchData.grupo,
                    matchData.fase
                  ])
                }

                periodMatches++

              } catch (error) {
                console.error(`❌ Erro ao sincronizar partida ${match.id}:`, error.message)
                totalErrors++
              }
            }

            competitionMatches += periodMatches
            await delay(SYNC_CONFIG.requestDelay)

          } catch (error) {
            console.error(`❌ Erro ao sincronizar ${period.name} de ${competition.nome}:`, error.message)
            totalErrors++
          }
        }

        console.log(`✅ ${competitionMatches} partidas sincronizadas para ${competition.nome}`)
        totalMatches += competitionMatches

        // Delay entre competições
        await delay(SYNC_CONFIG.requestDelay * 2)

      } catch (error) {
        console.error(`❌ Erro ao sincronizar partidas de ${competition.nome}:`, error.message)
        totalErrors++
      }
    }

    console.log(`✅ Total de ${totalMatches} partidas sincronizadas (${totalErrors} erros)`)
    await logSync('matches', 'complete', {
      synced: totalMatches,
      errors: totalErrors,
      competitions: competitions.length
    })

    return totalMatches

  } catch (error) {
    console.error('❌ Erro ao sincronizar partidas:', error)
    await logSync('matches', 'failed', { error: error.message })
    throw error
  }
}

// Função para sincronizar tudo
async function syncAll() {
  const startTime = Date.now()

  try {
    console.log('🚀 Iniciando sincronização COMPLETA com Football-Data.org API...')
    console.log('📅 Temporada atual:', getCurrentSeason())
    console.log('⏰ Início:', new Date().toLocaleString('pt-BR'))

    await initializeDatabase()
    await logSync('sync', 'start', { season: getCurrentSeason() })

    // 1. Sincronizar TODOS os campeonatos
    console.log('\n📋 ETAPA 1: Sincronizando campeonatos...')
    const competitionsCount = await syncCompetitions()

    // 2. Sincronizar times para TODAS as competições
    console.log('\n👥 ETAPA 2: Sincronizando times...')
    const competitions = await executeQuery(`
      SELECT id, api_id, nome FROM campeonatos
      WHERE status = 'ativo' AND api_id IS NOT NULL
      ORDER BY
        CASE WHEN codigo IN ('PL', 'PD', 'SA', 'BL1', 'FL1', 'CL', 'EL') THEN 0 ELSE 1 END,
        nome
    `)

    let totalTeams = 0
    for (let i = 0; i < competitions.length; i++) {
      const competition = competitions[i]
      console.log(`\n[${i + 1}/${competitions.length}] Processando ${competition.nome}...`)

      const teamsCount = await syncTeamsForCompetition(competition.api_id, competition.id, competition.nome)
      totalTeams += teamsCount

      // Delay progressivo entre competições
      await delay(SYNC_CONFIG.requestDelay * 3)
    }

    // 3. Sincronizar TODAS as partidas
    console.log('\n⚽ ETAPA 3: Sincronizando partidas...')
    const matchesCount = await syncMatches()

    // 4. Estatísticas finais
    const endTime = Date.now()
    const duration = Math.round((endTime - startTime) / 1000)

    console.log('\n🎉 SINCRONIZAÇÃO COMPLETA CONCLUÍDA!')
    console.log('=' .repeat(50))
    console.log(`📊 RESUMO FINAL:`)
    console.log(`   🏆 Campeonatos: ${competitionsCount}`)
    console.log(`   👥 Times: ${totalTeams}`)
    console.log(`   ⚽ Partidas: ${matchesCount}`)
    console.log(`   ⏱️  Duração: ${duration}s`)
    console.log(`   🏁 Término: ${new Date().toLocaleString('pt-BR')}`)

    await logSync('sync', 'complete', {
      competitions: competitionsCount,
      teams: totalTeams,
      matches: matchesCount,
      duration: duration
    })

    return {
      success: true,
      competitions: competitionsCount,
      teams: totalTeams,
      matches: matchesCount,
      duration: duration
    }

  } catch (error) {
    console.error('❌ ERRO CRÍTICO na sincronização:', error)
    await logSync('sync', 'failed', { error: error.message })
    throw error
  }
}

async function main() {
  try {
    const result = await syncAll()
    console.log('\n✅ Processo finalizado com sucesso!')
    process.exit(0)

  } catch (error) {
    console.error('\n💥 Processo finalizado com erro:', error.message)
    process.exit(1)
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { syncCompetitions, syncTeamsForCompetition, syncMatches, syncAll, main }
