/**
 * Utilitários para compatibilidade entre MySQL e SQLite
 */

// Forçar uso do MySQL conforme solicitado pelo usuário
const USE_SQLITE = false

/**
 * Adapta queries para funcionar tanto no MySQL quanto no SQLite
 */
export function adaptQuery(query) {
  // SEMPRE usar MySQL - nunca adaptar para SQLite
  return query
}

/**
 * Funções de data compatíveis
 */
export const DateFunctions = {
  now: () => "NOW()",
  currentDate: () => "CURDATE()",
  currentTimestamp: () => "CURRENT_TIMESTAMP",
  date: (field) => `DATE(${field})`,
  addDays: (field, days) => `DATE_ADD(${field}, INTERVAL ${days} DAY)`,
  subDays: (field, days) => `DATE_SUB(${field}, INTERVAL ${days} DAY)`
}

/**
 * Query builders para operações comuns
 */
export const QueryBuilder = {
  // Buscar campeonatos com estatísticas
  getCampeonatosWithStats: (filters = {}) => {
    const { status, pais, limit } = filters
    
    let query = `
      SELECT 
        c.*,
        COUNT(DISTINCT j.id) as total_jogos,
        COUNT(DISTINCT CASE WHEN j.status = 'agendado' AND j.data_jogo >= ${DateFunctions.now()} THEN j.id END) as jogos_futuros,
        COUNT(DISTINCT CASE WHEN j.status = 'finalizado' THEN j.id END) as jogos_finalizados,
        COUNT(DISTINCT CASE WHEN ${DateFunctions.date('j.data_jogo')} = ${DateFunctions.currentDate()} THEN j.id END) as jogos_hoje,
        COUNT(DISTINCT t.id) as total_times
      FROM campeonatos c
      LEFT JOIN jogos j ON c.id = j.campeonato_id
      LEFT JOIN times t ON (t.id = j.time_casa_id OR t.id = j.time_fora_id)
      WHERE 1=1
    `
    
    const params = []
    
    if (status && status !== 'todos') {
      query += ' AND c.status = ?'
      params.push(status)
    }
    
    if (pais && pais !== 'todos') {
      query += ' AND c.pais = ?'
      params.push(pais)
    }
    
    query += `
      GROUP BY c.id
      ORDER BY c.status DESC, c.data_criacao DESC
    `
    
    if (limit && limit > 0) {
      query += ' LIMIT ?'
      params.push(limit)
    }
    
    return { query: adaptQuery(query), params }
  },

  // Buscar jogos com filtros
  getJogosWithFilters: (filters = {}) => {
    const { campeonato, status, limit, dias } = filters
    
    let query = `
      SELECT 
        j.*,
        c.nome as campeonato_nome,
        c.descricao as campeonato_descricao,
        c.logo_url as campeonato_logo,
        c.pais as campeonato_pais,
        tc.nome as time_casa_nome,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tc.pais as time_casa_pais,
        tf.nome as time_fora_nome,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo,
        tf.pais as time_fora_pais
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      WHERE 1=1
    `
    
    const params = []
    
    if (status && status !== 'todos') {
      query += ' AND j.status = ?'
      params.push(status)
    }
    
    if (campeonato && campeonato !== 'todos') {
      query += ' AND c.id = ?'
      params.push(parseInt(campeonato))
    }
    
    if (status === 'agendado') {
      query += ` AND j.data_jogo >= ${DateFunctions.now()}`
      if (dias) {
        query += ` AND j.data_jogo <= ${DateFunctions.addDays(DateFunctions.now(), dias)}`
      }
    } else if (status === 'finalizado') {
      query += ` AND j.data_jogo <= ${DateFunctions.now()}`
    }
    
    query += ' ORDER BY j.data_jogo ASC'
    
    if (limit && limit > 0) {
      query += ' LIMIT ?'
      params.push(limit)
    }
    
    return { query: adaptQuery(query), params }
  },

  // Buscar estatísticas de jogos
  getJogosStats: () => {
    const query = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN j.status = 'agendado' AND j.data_jogo >= ${DateFunctions.now()} THEN 1 END) as agendados,
        COUNT(CASE WHEN j.status = 'ao_vivo' THEN 1 END) as ao_vivo,
        COUNT(CASE WHEN j.status = 'finalizado' THEN 1 END) as finalizados,
        COUNT(CASE WHEN ${DateFunctions.date('j.data_jogo')} = ${DateFunctions.currentDate()} THEN 1 END) as hoje
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE c.status = 'ativo'
    `
    
    return { query: adaptQuery(query), params: [] }
  },

  // Buscar times com estatísticas
  getTimesWithStats: (filters = {}) => {
    const { pais, search, limit } = filters
    
    let query = `
      SELECT 
        t.*,
        COUNT(DISTINCT jc.id) as jogos_casa,
        COUNT(DISTINCT jf.id) as jogos_fora,
        COUNT(DISTINCT jc.id) + COUNT(DISTINCT jf.id) as total_jogos,
        COUNT(DISTINCT CASE WHEN (jc.status = 'agendado' AND jc.data_jogo >= ${DateFunctions.now()}) OR (jf.status = 'agendado' AND jf.data_jogo >= ${DateFunctions.now()}) THEN COALESCE(jc.id, jf.id) END) as jogos_futuros
      FROM times t
      LEFT JOIN jogos jc ON t.id = jc.time_casa_id
      LEFT JOIN jogos jf ON t.id = jf.time_fora_id
      WHERE 1=1
    `
    
    const params = []
    
    if (pais && pais !== 'todos') {
      query += ' AND t.pais = ?'
      params.push(pais)
    }
    
    if (search) {
      query += ' AND (t.nome LIKE ? OR t.nome_curto LIKE ?)'
      params.push(`%${search}%`, `%${search}%`)
    }
    
    query += `
      GROUP BY t.id
      ORDER BY 
        CASE WHEN t.pais = 'Brasil' THEN 0 ELSE 1 END,
        total_jogos DESC,
        t.nome ASC
    `
    
    if (limit && limit > 0) {
      query += ' LIMIT ?'
      params.push(limit)
    }
    
    return { query: adaptQuery(query), params }
  }
}

export { USE_SQLITE }
