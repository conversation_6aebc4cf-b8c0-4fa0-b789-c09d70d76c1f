import Database from 'better-sqlite3'
import path from 'path'
import fs from 'fs'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

let db = null

// Configuração do banco SQLite
const dbPath = path.join(process.cwd(), 'database', 'sistema-bolao.sqlite')

// Função para inicializar o banco SQLite
export async function initializeDatabase() {
  try {
    // Criar diretório se não existir
    const dbDir = path.dirname(dbPath)
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true })
    }

    if (!db) {
      db = new Database(dbPath)
      db.pragma('journal_mode = WAL')
      db.pragma('foreign_keys = ON')
      console.log("✅ Banco SQLite inicializado com sucesso")
    }

    // Criar tabelas se não existirem
    await createTables()
    
    console.log("✅ Conexão com SQLite estabelecida com sucesso")
    return db
  } catch (error) {
    console.error("❌ Erro ao inicializar banco SQLite:", error)
    throw error
  }
}

// Função para criar tabelas
async function createTables() {
  const createTablesSQL = `
    -- Tabela de usuários
    CREATE TABLE IF NOT EXISTS usuarios (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nome TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        telefone TEXT,
        endereco TEXT,
        cpf_cnpj TEXT,
        senha_hash TEXT NOT NULL,
        tipo TEXT DEFAULT 'usuario' CHECK(tipo IN ('admin', 'usuario', 'cambista')),
        status TEXT DEFAULT 'ativo' CHECK(status IN ('ativo', 'inativo', 'bloqueado')),
        saldo REAL DEFAULT 0.00,
        data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
        ultimo_acesso DATETIME,
        data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- Tabela de campeonatos
    CREATE TABLE IF NOT EXISTS campeonatos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nome TEXT NOT NULL,
        descricao TEXT,
        pais TEXT,
        temporada TEXT,
        status TEXT DEFAULT 'ativo' CHECK(status IN ('ativo', 'encerrado', 'pausado')),
        data_inicio DATE,
        data_fim DATE,
        api_id TEXT UNIQUE,
        logo_url TEXT,
        codigo TEXT,
        tipo TEXT,
        plano TEXT,
        data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- Tabela de logs de sincronização
    CREATE TABLE IF NOT EXISTS sync_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        type TEXT NOT NULL,
        action TEXT NOT NULL,
        details TEXT,
        success BOOLEAN DEFAULT 1
    );

    -- Tabela de times
    CREATE TABLE IF NOT EXISTS times (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nome TEXT NOT NULL,
        nome_curto TEXT,
        cidade TEXT,
        estado TEXT,
        pais TEXT,
        logo_url TEXT,
        api_id TEXT UNIQUE,
        fundacao INTEGER,
        website TEXT,
        cores TEXT,
        data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- Tabela de jogos
    CREATE TABLE IF NOT EXISTS jogos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        campeonato_id INTEGER NOT NULL,
        time_casa_id INTEGER NOT NULL,
        time_fora_id INTEGER NOT NULL,
        data_jogo DATETIME NOT NULL,
        local_jogo TEXT,
        rodada INTEGER,
        resultado_casa INTEGER,
        resultado_fora INTEGER,
        status TEXT DEFAULT 'agendado' CHECK(status IN ('agendado', 'ao_vivo', 'finalizado', 'cancelado', 'adiado', 'suspenso')),
        api_id TEXT UNIQUE,
        grupo TEXT,
        fase TEXT,
        data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (campeonato_id) REFERENCES campeonatos(id) ON DELETE CASCADE,
        FOREIGN KEY (time_casa_id) REFERENCES times(id),
        FOREIGN KEY (time_fora_id) REFERENCES times(id)
    );

    -- Tabela de bolões
    CREATE TABLE IF NOT EXISTS boloes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nome TEXT NOT NULL,
        descricao TEXT,
        valor_aposta REAL NOT NULL,
        premio_total REAL NOT NULL,
        max_participantes INTEGER,
        min_acertos INTEGER DEFAULT 3,
        data_inicio DATETIME NOT NULL,
        data_fim DATETIME NOT NULL,
        status TEXT DEFAULT 'em_breve' CHECK(status IN ('ativo', 'encerrado', 'em_breve')),
        criado_por INTEGER NOT NULL,
        regras TEXT,
        campeonatos_selecionados TEXT, -- JSON
        partidas_selecionadas TEXT, -- JSON
        data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (criado_por) REFERENCES usuarios(id)
    );

    -- Tabela de apostas
    CREATE TABLE IF NOT EXISTS apostas (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        usuario_id INTEGER NOT NULL,
        bolao_id INTEGER NOT NULL,
        valor_total REAL NOT NULL,
        status TEXT DEFAULT 'pendente' CHECK(status IN ('pendente', 'paga', 'cancelada')),
        data_aposta DATETIME DEFAULT CURRENT_TIMESTAMP,
        data_pagamento DATETIME,
        codigo_bilhete TEXT UNIQUE,
        data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
        FOREIGN KEY (bolao_id) REFERENCES boloes(id)
    );

    -- Tabela de associação bolão-jogos
    CREATE TABLE IF NOT EXISTS bolao_jogos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bolao_id INTEGER NOT NULL,
        jogo_id INTEGER NOT NULL,
        data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (bolao_id) REFERENCES boloes(id) ON DELETE CASCADE,
        FOREIGN KEY (jogo_id) REFERENCES jogos(id) ON DELETE CASCADE,
        UNIQUE(bolao_id, jogo_id)
    );

    -- Inserir dados iniciais
    INSERT OR IGNORE INTO usuarios (id, nome, email, senha_hash, tipo) VALUES
    (1, 'Administrador', '<EMAIL>', '$2b$10$hash_da_senha_admin123', 'admin');

    -- Campeonatos iniciais para 2025
    INSERT OR IGNORE INTO campeonatos (nome, descricao, pais, temporada, status, data_inicio, data_fim) VALUES
    ('Premier League', 'Campeonato Inglês 2024/25', 'Inglaterra', '2025', 'ativo', '2024-08-17', '2025-05-25'),
    ('La Liga', 'Campeonato Espanhol 2024/25', 'Espanha', '2025', 'ativo', '2024-08-18', '2025-05-25'),
    ('Serie A', 'Campeonato Italiano 2024/25', 'Itália', '2025', 'ativo', '2024-08-18', '2025-05-25'),
    ('Bundesliga', 'Campeonato Alemão 2024/25', 'Alemanha', '2025', 'ativo', '2024-08-24', '2025-05-17'),
    ('Ligue 1', 'Campeonato Francês 2024/25', 'França', '2025', 'ativo', '2024-08-16', '2025-05-18'),
    ('Brasileirão Série A', 'Campeonato Brasileiro 2025', 'Brasil', '2025', 'ativo', '2025-04-12', '2025-12-07'),
    ('Copa Libertadores', 'Copa Libertadores 2025', 'América do Sul', '2025', 'ativo', '2025-02-05', '2025-11-29'),
    ('UEFA Champions League', 'Liga dos Campeões 2024/25', 'Europa', '2025', 'ativo', '2024-09-17', '2025-05-31');

    -- Times principais
    INSERT OR IGNORE INTO times (nome, nome_curto, cidade, pais) VALUES
    ('Manchester City', 'MCI', 'Manchester', 'Inglaterra'),
    ('Arsenal', 'ARS', 'Londres', 'Inglaterra'),
    ('Liverpool', 'LIV', 'Liverpool', 'Inglaterra'),
    ('Chelsea', 'CHE', 'Londres', 'Inglaterra'),
    ('Manchester United', 'MUN', 'Manchester', 'Inglaterra'),
    ('Real Madrid', 'RMA', 'Madrid', 'Espanha'),
    ('Barcelona', 'BAR', 'Barcelona', 'Espanha'),
    ('Atlético Madrid', 'ATM', 'Madrid', 'Espanha'),
    ('Bayern Munich', 'BAY', 'Munique', 'Alemanha'),
    ('Borussia Dortmund', 'BVB', 'Dortmund', 'Alemanha'),
    ('Paris Saint-Germain', 'PSG', 'Paris', 'França'),
    ('Juventus', 'JUV', 'Turim', 'Itália'),
    ('AC Milan', 'MIL', 'Milão', 'Itália'),
    ('Inter Milan', 'INT', 'Milão', 'Itália'),
    ('Flamengo', 'FLA', 'Rio de Janeiro', 'Brasil'),
    ('Palmeiras', 'PAL', 'São Paulo', 'Brasil'),
    ('São Paulo', 'SAO', 'São Paulo', 'Brasil'),
    ('Corinthians', 'COR', 'São Paulo', 'Brasil'),
    ('Atlético Mineiro', 'CAM', 'Belo Horizonte', 'Brasil'),
    ('Fluminense', 'FLU', 'Rio de Janeiro', 'Brasil');
  `

  try {
    db.exec(createTablesSQL)
    console.log("✅ Tabelas criadas/verificadas com sucesso")
  } catch (error) {
    console.error("❌ Erro ao criar tabelas:", error)
    throw error
  }
}

// Função para obter o banco
export async function getDatabase() {
  if (!db) {
    await initializeDatabase()
  }
  return db
}

// Função para executar queries
export async function executeQuery(query, params = []) {
  try {
    const database = await getDatabase()
    
    if (query.trim().toUpperCase().startsWith('SELECT')) {
      const stmt = database.prepare(query)
      return stmt.all(params)
    } else {
      const stmt = database.prepare(query)
      return stmt.run(params)
    }
  } catch (error) {
    console.error("❌ Erro ao executar query:", error)
    console.error("Query:", query)
    console.error("Params:", params)
    throw error
  }
}

// Função para executar query que retorna uma única linha
export async function executeQuerySingle(query, params = []) {
  try {
    const results = await executeQuery(query, params)
    return Array.isArray(results) && results.length > 0 ? results[0] : null
  } catch (error) {
    console.error("❌ Erro ao executar query single:", error)
    throw error
  }
}

// Funções específicas (mantendo compatibilidade)
export async function getCampeonatos() {
  try {
    const campeonatos = await executeQuery("SELECT * FROM campeonatos ORDER BY data_criacao DESC")
    return campeonatos || []
  } catch (error) {
    console.error("Erro ao buscar campeonatos:", error)
    return []
  }
}

export async function getCampeonatosStats() {
  try {
    const stats = await executeQuerySingle(`
      SELECT 
        COUNT(CASE WHEN status = 'ativo' THEN 1 END) as ativos,
        COUNT(CASE WHEN date(data_criacao) = date('now') THEN 1 END) as jogosHoje,
        COUNT(CASE WHEN pais != 'Brasil' THEN 1 END) as internacionais
      FROM campeonatos
    `)
    
    return {
      ativos: stats?.ativos || 0,
      jogosHoje: stats?.jogosHoje || 0,
      internacionais: stats?.internacionais || 0
    }
  } catch (error) {
    console.error("Erro ao buscar stats de campeonatos:", error)
    return { ativos: 0, jogosHoje: 0, internacionais: 0 }
  }
}

// ==================== FUNÇÕES PARA USUÁRIOS ====================

export async function getUsuarios(filters = {}) {
  try {
    let query = "SELECT * FROM usuarios WHERE 1=1"
    const params = []

    if (filters.status && filters.status !== "todos") {
      query += " AND status = ?"
      params.push(filters.status)
    }

    if (filters.tipo && filters.tipo !== "todos") {
      query += " AND tipo = ?"
      params.push(filters.tipo)
    }

    if (filters.search) {
      query += " AND (nome LIKE ? OR email LIKE ?)"
      params.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    query += " ORDER BY data_cadastro DESC"

    const usuarios = await executeQuery(query, params)
    return usuarios || []
  } catch (error) {
    console.error("Erro ao buscar usuários:", error)
    return []
  }
}

export async function getUsuariosStats() {
  try {
    const stats = await executeQuerySingle(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'ativo' THEN 1 END) as ativos,
        COUNT(CASE WHEN tipo = 'cambista' THEN 1 END) as cambistas,
        COUNT(CASE WHEN status = 'bloqueado' THEN 1 END) as bloqueados
      FROM usuarios
    `)

    return {
      total: stats?.total || 0,
      ativos: stats?.ativos || 0,
      cambistas: stats?.cambistas || 0,
      bloqueados: stats?.bloqueados || 0
    }
  } catch (error) {
    console.error("Erro ao buscar stats de usuários:", error)
    return { total: 0, ativos: 0, cambistas: 0, bloqueados: 0 }
  }
}

// ==================== FUNÇÕES PARA CAMBISTAS ====================

export async function getCambistas(filters = {}) {
  try {
    let query = "SELECT * FROM usuarios WHERE tipo = 'cambista'"
    const params = []

    if (filters.search) {
      query += " AND (nome LIKE ? OR email LIKE ?)"
      params.push(`%${filters.search}%`, `%${filters.search}%`)
    }

    query += " ORDER BY data_cadastro DESC"

    const cambistas = await executeQuery(query, params)
    return cambistas || []
  } catch (error) {
    console.error("Erro ao buscar cambistas:", error)
    return []
  }
}

export async function getCambistasStats() {
  try {
    const [ativos, vendas] = await Promise.all([
      executeQuerySingle("SELECT COUNT(*) as count FROM usuarios WHERE tipo = 'cambista' AND status = 'ativo'")
        .catch(() => ({ count: 0 })),
      executeQuerySingle(`
        SELECT
          COALESCE(SUM(a.valor_total), 0) as total_vendas,
          COALESCE(SUM(CASE WHEN DATE(a.data_aposta) = DATE('now') THEN a.valor_total ELSE 0 END), 0) as vendas_hoje
        FROM apostas a
        JOIN usuarios u ON a.usuario_id = u.id
        WHERE u.tipo = 'cambista'
      `)
        .catch(() => ({ total_vendas: 0, vendas_hoje: 0 })),
    ])

    return {
      ativos: ativos?.count || 0,
      totalVendas: vendas?.total_vendas || 0,
      vendasHoje: vendas?.vendas_hoje || 0,
      totalComissoes: (vendas?.total_vendas || 0) * 0.05, // 5% de comissão
    }
  } catch (error) {
    console.error("Erro ao buscar stats de cambistas:", error)
    return { ativos: 0, totalVendas: 0, vendasHoje: 0, totalComissoes: 0 }
  }
}

export async function createCambista(cambista) {
  try {
    const result = await executeQuery(`
      INSERT INTO usuarios (nome, email, telefone, senha_hash, tipo, status)
      VALUES (?, ?, ?, ?, 'cambista', 'ativo')
    `, [
      cambista.nome,
      cambista.email,
      cambista.telefone,
      cambista.senha_hash
    ])

    return result.lastInsertRowid
  } catch (error) {
    console.error("Erro ao criar cambista:", error)
    throw error
  }
}

// ==================== FUNÇÕES PARA BOLÕES ====================

export async function getBoloes() {
  try {
    const boloes = await executeQuery(`
      SELECT
        b.*,
        COALESCE(u.nome, 'Usuário não encontrado') as criado_por_nome
      FROM boloes b
      LEFT JOIN usuarios u ON b.criado_por = u.id
      ORDER BY b.data_criacao DESC
    `)

    return boloes || []
  } catch (error) {
    console.error("Erro ao buscar bolões:", error)
    return []
  }
}

export async function getBoloesStats() {
  try {
    const [ativos, participantes, faturamento, finalizandoHoje] = await Promise.all([
      executeQuerySingle("SELECT COUNT(*) as count FROM boloes WHERE status = 'ativo'").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(DISTINCT usuario_id) as count FROM apostas").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COALESCE(SUM(valor_total), 0) as total FROM apostas WHERE status = 'paga'")
        .catch(() => ({ total: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM boloes WHERE DATE(data_fim) = DATE('now')")
        .catch(() => ({ count: 0 })),
    ])

    return {
      ativos: ativos?.count || 0,
      participantes: participantes?.count || 0,
      faturamento: faturamento?.total || 0,
      finalizandoHoje: finalizandoHoje?.count || 0,
    }
  } catch (error) {
    console.error("Erro ao buscar stats de bolões:", error)
    return { ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 }
  }
}

export async function createBolao(bolaoData) {
  try {
    console.log("🔍 Dados recebidos para criar bolão:", bolaoData)

    const {
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes,
      min_acertos,
      data_inicio,
      data_fim,
      status,
      criado_por,
      regras,
      campeonatos_selecionados,
      partidas_selecionadas
    } = bolaoData

    // Preparar os parâmetros para a query
    const params = [
      nome,
      descricao,
      valor_aposta,
      premio_total,
      max_participantes || 100,
      min_acertos || 3,
      data_inicio,
      data_fim,
      status || 'ativo',
      criado_por,
      regras ? JSON.stringify(regras) : null,
      campeonatos_selecionados ? JSON.stringify(campeonatos_selecionados) : null,
      partidas_selecionadas ? JSON.stringify(partidas_selecionadas) : null
    ]

    console.log("🔍 Parâmetros da query:", params)
    console.log("🔍 Número de parâmetros:", params.length)

    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras,
        campeonatos_selecionados, partidas_selecionadas
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, params)

    const bolaoId = result.lastInsertRowid
    console.log("✅ Bolão criado com ID:", bolaoId)

    // Associar partidas ao bolão na tabela bolao_jogos
    if (partidas_selecionadas && Array.isArray(partidas_selecionadas) && partidas_selecionadas.length > 0) {
      console.log("🔍 Associando partidas ao bolão:", partidas_selecionadas)

      for (const partida of partidas_selecionadas) {
        // Verificar se partida é um objeto ou apenas um ID
        const partidaId = typeof partida === 'object' ? partida.id : partida

        if (partidaId) {
          // Verificar se o jogo existe antes de tentar associar
          const jogoExiste = await executeQuerySingle('SELECT id FROM jogos WHERE id = ?', [partidaId])

          if (jogoExiste) {
            console.log("🔍 Inserindo partida ID:", partidaId)
            await executeQuery(`
              INSERT INTO bolao_jogos (bolao_id, jogo_id) VALUES (?, ?)
            `, [bolaoId, partidaId])
          } else {
            console.warn("⚠️ Jogo com ID", partidaId, "não encontrado na tabela jogos. Pulando...")
          }
        }
      }
    }

    return bolaoId
  } catch (error) {
    console.error("❌ Erro ao criar bolão:", error)
    console.error("❌ Stack trace:", error.stack)
    throw error
  }
}

// ==================== FUNÇÕES PARA JOGOS ====================

export async function getJogos(filters = {}) {
  try {
    let query = `
      SELECT
        j.*,
        c.nome as campeonato_nome,
        tc.nome as time_casa_nome,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora_nome,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      WHERE 1=1
    `
    const params = []

    if (filters.campeonato_id) {
      query += " AND j.campeonato_id = ?"
      params.push(filters.campeonato_id)
    }

    if (filters.status) {
      query += " AND j.status = ?"
      params.push(filters.status)
    }

    if (filters.data_inicio && filters.data_fim) {
      query += " AND DATE(j.data_jogo) BETWEEN ? AND ?"
      params.push(filters.data_inicio, filters.data_fim)
    } else if (filters.data_inicio) {
      query += " AND DATE(j.data_jogo) >= ?"
      params.push(filters.data_inicio)
    } else if (filters.data_fim) {
      query += " AND DATE(j.data_jogo) <= ?"
      params.push(filters.data_fim)
    }

    query += " ORDER BY j.data_jogo ASC"

    if (filters.limit) {
      query += " LIMIT ?"
      params.push(parseInt(filters.limit))
    }

    const jogos = await executeQuery(query, params)
    return jogos || []
  } catch (error) {
    console.error("Erro ao buscar jogos:", error)
    return []
  }
}

export async function getJogosStats() {
  try {
    const [hoje, semana, total, aoVivo] = await Promise.all([
      executeQuerySingle("SELECT COUNT(*) as count FROM jogos WHERE DATE(data_jogo) = DATE('now')").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM jogos WHERE data_jogo BETWEEN DATE('now') AND DATE('now', '+7 days')").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM jogos").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM jogos WHERE status = 'ao_vivo'").catch(() => ({ count: 0 })),
    ])

    return {
      hoje: hoje?.count || 0,
      semana: semana?.count || 0,
      total: total?.count || 0,
      aoVivo: aoVivo?.count || 0,
    }
  } catch (error) {
    console.error("Erro ao buscar stats de jogos:", error)
    return { hoje: 0, semana: 0, total: 0, aoVivo: 0 }
  }
}

// ==================== FUNÇÕES PARA DASHBOARD ====================

export async function getDashboardStats() {
  try {
    const [boloes, usuarios, faturamento, apostas, cambistas, jogosHoje] = await Promise.all([
      executeQuerySingle("SELECT COUNT(*) as count FROM boloes").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM usuarios WHERE status = 'ativo'").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COALESCE(SUM(valor_total), 0) as total FROM apostas WHERE status = 'paga' AND strftime('%m', data_aposta) = strftime('%m', 'now')").catch(() => ({ total: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM apostas WHERE DATE(data_aposta) = DATE('now')").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM usuarios WHERE tipo = 'cambista' AND status = 'ativo'").catch(() => ({ count: 0 })),
      executeQuerySingle("SELECT COUNT(*) as count FROM jogos WHERE DATE(data_jogo) = DATE('now')").catch(() => ({ count: 0 })),
    ])

    return {
      totalBoloes: boloes?.count || 0,
      totalUsuarios: usuarios?.count || 0,
      faturamentoMes: faturamento?.total || 0,
      apostasHoje: apostas?.count || 0,
      cambistasAtivos: cambistas?.count || 0,
      jogosHoje: jogosHoje?.count || 0,
    }
  } catch (error) {
    console.error("Erro ao buscar stats do dashboard:", error)
    return {
      totalBoloes: 0,
      totalUsuarios: 0,
      faturamentoMes: 0,
      apostasHoje: 0,
      cambistasAtivos: 0,
      jogosHoje: 0,
    }
  }
}

// Função para fechar o banco
export async function closeDatabase() {
  if (db) {
    db.close()
    db = null
  }
}
