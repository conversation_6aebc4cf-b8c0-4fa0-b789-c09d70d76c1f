import { executeQuery, initializeDatabase } from '../lib/database.js'

async function populateMySQL() {
  try {
    console.log('🚀 Populando MySQL com dados iniciais...')
    
    await initializeDatabase()
    
    // 1. Inserir campeonatos
    console.log('🏆 Inserindo campeonatos...')
    
    const campeonatos = [
      {
        nome: 'Brasileirão Série A',
        descricao: 'Campeonato Brasileiro de Futebol - Série A',
        pais: 'Brasil',
        status: 'ativo',
        logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/brasileirao-vector-logo.png'
      },
      {
        nome: 'Copa do Brasil',
        descricao: 'Copa do Brasil de Futebol',
        pais: 'Brasil',
        status: 'ativo',
        logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/copa-do-brasil-vector-logo.png'
      },
      {
        nome: 'Copa Libertadores',
        descricao: 'Copa Libertadores da América',
        pais: 'América do Sul',
        status: 'ativo',
        logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/copa-libertadores-vector-logo.png'
      },
      {
        nome: 'Copa Sul-Americana',
        descricao: 'Copa Sul-Americana',
        pais: 'América do Sul',
        status: 'ativo',
        logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/copa-sudamericana-vector-logo.png'
      },
      {
        nome: 'Premier League',
        descricao: 'Premier League Inglesa',
        pais: 'Inglaterra',
        status: 'ativo',
        logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/premier-league-vector-logo.png'
      }
    ]
    
    for (const campeonato of campeonatos) {
      await executeQuery(`
        INSERT INTO campeonatos (nome, descricao, pais, status, logo_url, data_criacao)
        VALUES (?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        descricao = VALUES(descricao),
        logo_url = VALUES(logo_url),
        data_atualizacao = NOW()
      `, [campeonato.nome, campeonato.descricao, campeonato.pais, campeonato.status, campeonato.logo_url])
    }
    
    console.log(`✅ ${campeonatos.length} campeonatos inseridos`)
    
    // 2. Inserir times
    console.log('⚽ Inserindo times...')
    
    const times = [
      // Times brasileiros
      { nome: 'Flamengo', nome_curto: 'FLA', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/flamengo-vector-logo.png' },
      { nome: 'Palmeiras', nome_curto: 'PAL', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/palmeiras-vector-logo.png' },
      { nome: 'São Paulo', nome_curto: 'SAO', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/sao-paulo-vector-logo.png' },
      { nome: 'Corinthians', nome_curto: 'COR', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/corinthians-vector-logo.png' },
      { nome: 'Santos', nome_curto: 'SAN', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/santos-vector-logo.png' },
      { nome: 'Grêmio', nome_curto: 'GRE', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/gremio-vector-logo.png' },
      { nome: 'Internacional', nome_curto: 'INT', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/internacional-vector-logo.png' },
      { nome: 'Atlético-MG', nome_curto: 'ATM', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/atletico-mg-vector-logo.png' },
      { nome: 'Cruzeiro', nome_curto: 'CRU', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/cruzeiro-vector-logo.png' },
      { nome: 'Botafogo', nome_curto: 'BOT', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/botafogo-vector-logo.png' },
      { nome: 'Vasco', nome_curto: 'VAS', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/vasco-vector-logo.png' },
      { nome: 'Fluminense', nome_curto: 'FLU', pais: 'Brasil', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/fluminense-vector-logo.png' },
      
      // Times internacionais
      { nome: 'Manchester City', nome_curto: 'MCI', pais: 'Inglaterra', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/manchester-city-vector-logo.png' },
      { nome: 'Manchester United', nome_curto: 'MUN', pais: 'Inglaterra', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/manchester-united-vector-logo.png' },
      { nome: 'Liverpool', nome_curto: 'LIV', pais: 'Inglaterra', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/liverpool-vector-logo.png' },
      { nome: 'Arsenal', nome_curto: 'ARS', pais: 'Inglaterra', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/arsenal-vector-logo.png' },
      { nome: 'Chelsea', nome_curto: 'CHE', pais: 'Inglaterra', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/chelsea-vector-logo.png' },
      { nome: 'Tottenham', nome_curto: 'TOT', pais: 'Inglaterra', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/tottenham-vector-logo.png' },
      
      // Times sul-americanos
      { nome: 'Boca Juniors', nome_curto: 'BOC', pais: 'Argentina', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/boca-juniors-vector-logo.png' },
      { nome: 'River Plate', nome_curto: 'RIV', pais: 'Argentina', logo_url: 'https://logoeps.com/wp-content/uploads/2013/03/river-plate-vector-logo.png' }
    ]
    
    for (const time of times) {
      await executeQuery(`
        INSERT INTO times (nome, nome_curto, pais, logo_url, data_criacao)
        VALUES (?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        nome_curto = VALUES(nome_curto),
        logo_url = VALUES(logo_url),
        data_atualizacao = NOW()
      `, [time.nome, time.nome_curto, time.pais, time.logo_url])
    }
    
    console.log(`✅ ${times.length} times inseridos`)
    
    // 3. Criar jogos de exemplo para TODOS os campeonatos
    console.log('🎮 Criando jogos de exemplo...')

    // Buscar todos os campeonatos e times
    const todosCampeonatos = await executeQuery("SELECT id, nome FROM campeonatos ORDER BY id")
    const todosTimes = await executeQuery("SELECT id, nome, pais FROM times ORDER BY nome")

    if (todosCampeonatos.length > 0 && todosTimes.length >= 4) {
      let totalJogos = 0

      for (const campeonato of todosCampeonatos) {
        console.log(`🏆 Criando jogos para ${campeonato.nome}...`)

        // Filtrar times por país para cada campeonato
        let timesParaCampeonato = []

        if (campeonato.nome.includes('Brasileirão') || campeonato.nome.includes('Copa do Brasil')) {
          timesParaCampeonato = todosTimes.filter(t => t.pais === 'Brasil')
        } else if (campeonato.nome.includes('Premier League')) {
          timesParaCampeonato = todosTimes.filter(t => t.pais === 'Inglaterra')
        } else {
          // Para Libertadores e Sul-Americana, usar times brasileiros e argentinos
          timesParaCampeonato = todosTimes.filter(t => ['Brasil', 'Argentina'].includes(t.pais))
        }

        // Criar pelo menos 6 jogos por campeonato
        const numJogos = Math.min(6, Math.floor(timesParaCampeonato.length / 2))

        for (let i = 0; i < numJogos; i++) {
          const timeCasaIndex = (i * 2) % timesParaCampeonato.length
          const timeForaIndex = (i * 2 + 1) % timesParaCampeonato.length

          if (timeCasaIndex !== timeForaIndex && timesParaCampeonato[timeCasaIndex] && timesParaCampeonato[timeForaIndex]) {
            const dataJogo = new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000) // Espalhar pelos próximos dias

            await executeQuery(`
              INSERT INTO jogos (campeonato_id, time_casa_id, time_fora_id, data_jogo, status, data_criacao)
              VALUES (?, ?, ?, ?, 'agendado', NOW())
            `, [
              campeonato.id,
              timesParaCampeonato[timeCasaIndex].id,
              timesParaCampeonato[timeForaIndex].id,
              dataJogo
            ])

            totalJogos++
            console.log(`  ⚽ ${timesParaCampeonato[timeCasaIndex].nome} vs ${timesParaCampeonato[timeForaIndex].nome}`)
          }
        }
      }

      console.log(`✅ ${totalJogos} jogos criados no total`)
    }
    
    // 4. Verificar dados inseridos
    console.log('\n📊 Verificando dados inseridos...')
    
    const [campeonatosCount, timesCount, jogosCount] = await Promise.all([
      executeQuery('SELECT COUNT(*) as count FROM campeonatos'),
      executeQuery('SELECT COUNT(*) as count FROM times'),
      executeQuery('SELECT COUNT(*) as count FROM jogos')
    ])
    
    console.log(`✅ Campeonatos: ${campeonatosCount[0].count}`)
    console.log(`✅ Times: ${timesCount[0].count}`)
    console.log(`✅ Jogos: ${jogosCount[0].count}`)
    
    console.log('\n🎉 População do MySQL concluída com sucesso!')
    
  } catch (error) {
    console.error('❌ Erro ao popular MySQL:', error)
    process.exit(1)
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  populateMySQL()
}

export default populateMySQL
