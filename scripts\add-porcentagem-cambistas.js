import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function addPorcentagemCambistas() {
  try {
    console.log('🔧 Adicionando campo porcentagem_comissao para cambistas...')
    
    await initializeDatabase()
    
    // Verificar se a coluna já existe
    try {
      await executeQuery(`
        ALTER TABLE usuarios 
        ADD COLUMN porcentagem_comissao DECIMAL(5,2) DEFAULT 0.00
      `)
      console.log('✅ Campo porcentagem_comissao adicionado na tabela usuarios')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('⚠️ Campo porcentagem_comissao já existe na tabela usuarios')
      } else {
        throw error
      }
    }

    // Atualizar cambistas existentes com porcentagem padrão
    await executeQuery(`
      UPDATE usuarios 
      SET porcentagem_comissao = 5.00 
      WHERE tipo = 'cambista' AND porcentagem_comissao IS NULL
    `)
    
    console.log('✅ Porcentagem padrão (5%) definida para cambistas existentes')
    console.log('🎉 Configuração de porcentagem para cambistas concluída!')
    
  } catch (error) {
    console.error('❌ Erro ao configurar porcentagem para cambistas:', error)
    process.exit(1)
  }
}

addPorcentagemCambistas()
