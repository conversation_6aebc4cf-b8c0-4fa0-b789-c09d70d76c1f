"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"

export function TestCambistaForm() {
  const [formData, setFormData] = useState({
    nome: "João Teste Silva",
    email: "<EMAIL>",
    telefone: "(11) 98765-4321",
    endereco: "Av. Teste, 456 - <PERSON>rro Teste - São Paulo/SP",
    cpf_cnpj: "987.654.321-00",
    senha: "teste123",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [result, setResult] = useState(null)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setResult(null)

    try {
      console.log("Enviando dados:", formData)

      const response = await fetch("/api/admin/cambistas", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()
      console.log("Resposta recebida:", data)

      if (data.success) {
        toast.success("Cambista criado com sucesso!")
        setResult({ success: true, data })

        // Limpar formulário
        setFormData({
          nome: "",
          email: "",
          telefone: "",
          endereco: "",
          cpf_cnpj: "",
          senha: "",
        })
      } else {
        toast.error(data.error || "Erro ao criar cambista")
        setResult({ success: false, error: data.error })
      }
    } catch (error) {
      console.error("Erro:", error)
      toast.error("Erro de conexão")
      setResult({ success: false, error: error.message })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Teste - Criar Cambista</CardTitle>
        <CardDescription>
          Formulário de teste para verificar se os novos campos (endereço e CPF/CNPJ) estão funcionando
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="nome">Nome *</Label>
              <Input
                id="nome"
                value={formData.nome}
                onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="telefone">Telefone *</Label>
              <Input
                id="telefone"
                value={formData.telefone}
                onChange={(e) => setFormData({ ...formData, telefone: e.target.value })}
                placeholder="(11) 99999-9999"
                required
              />
            </div>
            <div>
              <Label htmlFor="cpf_cnpj">CPF/CNPJ</Label>
              <Input
                id="cpf_cnpj"
                value={formData.cpf_cnpj}
                onChange={(e) => setFormData({ ...formData, cpf_cnpj: e.target.value })}
                placeholder="000.000.000-00"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="endereco">Endereço Completo</Label>
            <Input
              id="endereco"
              value={formData.endereco}
              onChange={(e) => setFormData({ ...formData, endereco: e.target.value })}
              placeholder="Rua, número - Bairro - Cidade/UF - CEP"
            />
          </div>

          <div>
            <Label htmlFor="senha">Senha *</Label>
            <Input
              id="senha"
              type="password"
              value={formData.senha}
              onChange={(e) => setFormData({ ...formData, senha: e.target.value })}
              required
            />
          </div>

          <Button type="submit" disabled={isSubmitting} className="w-full">
            {isSubmitting ? "Criando..." : "Criar Cambista de Teste"}
          </Button>
        </form>

        {result && (
          <div
            className={`mt-4 p-4 rounded-lg ${result.success ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"}`}
          >
            <h3 className={`font-semibold ${result.success ? "text-green-800" : "text-red-800"}`}>
              {result.success ? "✅ Sucesso!" : "❌ Erro!"}
            </h3>
            <pre className={`mt-2 text-sm ${result.success ? "text-green-700" : "text-red-700"}`}>
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-semibold text-blue-800 mb-2">Status dos Novos Campos:</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>✅ Campo Endereço: Implementado</li>
            <li>✅ Campo CPF/CNPJ: Implementado</li>
            <li>✅ Validação: CPF (11 dígitos) ou CNPJ (14 dígitos)</li>
            <li>✅ Banco de Dados: Colunas adicionadas</li>
            <li>✅ API: Processamento implementado</li>
            <li>✅ Interface: Campos visíveis no formulário</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
