import { executeQuery, initializeDatabase } from './lib/database.js'

async function testConnection() {
  try {
    console.log('🔄 Testando conexão MySQL...')
    
    await initializeDatabase()
    console.log('✅ Conexão estabelecida')
    
    // Verificar tabelas existentes
    const tables = await executeQuery(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'sistema-bolao-top'
      ORDER BY TABLE_NAME
    `)
    
    console.log('📊 Tabelas encontradas:')
    tables.forEach(table => {
      console.log(`  - ${table.TABLE_NAME}`)
    })
    
    // Verificar dados existentes
    const [campeonatos, times, jogos] = await Promise.all([
      executeQuery('SELECT COUNT(*) as count FROM campeonatos'),
      executeQuery('SELECT COUNT(*) as count FROM times'),
      executeQuery('SELECT COUNT(*) as count FROM jogos')
    ])
    
    console.log('\n📈 Dados atuais:')
    console.log(`  🏆 Campeonatos: ${campeonatos[0].count}`)
    console.log(`  ⚽ Times: ${times[0].count}`)
    console.log(`  🎮 Jogos: ${jogos[0].count}`)
    
    // Se não há dados, inserir alguns básicos
    if (campeonatos[0].count === 0) {
      console.log('\n🔄 Inserindo dados básicos...')
      
      // Inserir campeonatos
      await executeQuery(`
        INSERT INTO campeonatos (nome, descricao, pais, status, data_criacao)
        VALUES 
        ('Brasileirão Série A', 'Campeonato Brasileiro de Futebol - Série A', 'Brasil', 'ativo', NOW()),
        ('Copa do Brasil', 'Copa do Brasil de Futebol', 'Brasil', 'ativo', NOW()),
        ('Copa Libertadores', 'Copa Libertadores da América', 'América do Sul', 'ativo', NOW())
      `)
      
      // Inserir times
      await executeQuery(`
        INSERT INTO times (nome, nome_curto, pais, data_criacao)
        VALUES 
        ('Flamengo', 'FLA', 'Brasil', NOW()),
        ('Palmeiras', 'PAL', 'Brasil', NOW()),
        ('São Paulo', 'SAO', 'Brasil', NOW()),
        ('Corinthians', 'COR', 'Brasil', NOW()),
        ('Santos', 'SAN', 'Brasil', NOW()),
        ('Grêmio', 'GRE', 'Brasil', NOW())
      `)
      
      // Buscar IDs para criar jogos
      const campeonatosData = await executeQuery('SELECT id, nome FROM campeonatos LIMIT 3')
      const timesData = await executeQuery('SELECT id, nome FROM times LIMIT 6')
      
      if (campeonatosData.length > 0 && timesData.length >= 4) {
        // Criar jogos para cada campeonato
        for (let i = 0; i < campeonatosData.length; i++) {
          const campeonato = campeonatosData[i]
          
          // Criar 3 jogos por campeonato
          for (let j = 0; j < 3; j++) {
            const timeCasa = timesData[j * 2]
            const timeFora = timesData[j * 2 + 1]
            
            if (timeCasa && timeFora) {
              const dataJogo = new Date(Date.now() + (i * 3 + j + 1) * 24 * 60 * 60 * 1000)
              
              await executeQuery(`
                INSERT INTO jogos (campeonato_id, time_casa_id, time_fora_id, data_jogo, status, data_criacao)
                VALUES (?, ?, ?, ?, 'agendado', NOW())
              `, [campeonato.id, timeCasa.id, timeFora.id, dataJogo])
              
              console.log(`  ⚽ ${timeCasa.nome} vs ${timeFora.nome} (${campeonato.nome})`)
            }
          }
        }
      }
      
      console.log('✅ Dados básicos inseridos')
    }
    
    // Verificar dados finais
    const [finalCampeonatos, finalTimes, finalJogos] = await Promise.all([
      executeQuery('SELECT COUNT(*) as count FROM campeonatos'),
      executeQuery('SELECT COUNT(*) as count FROM times'),
      executeQuery('SELECT COUNT(*) as count FROM jogos')
    ])
    
    console.log('\n🎉 Dados finais:')
    console.log(`  🏆 Campeonatos: ${finalCampeonatos[0].count}`)
    console.log(`  ⚽ Times: ${finalTimes[0].count}`)
    console.log(`  🎮 Jogos: ${finalJogos[0].count}`)
    
    // Listar alguns jogos
    const jogosExemplo = await executeQuery(`
      SELECT 
        j.id,
        c.nome as campeonato,
        tc.nome as time_casa,
        tf.nome as time_fora,
        j.data_jogo,
        j.status
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      ORDER BY j.data_jogo ASC
      LIMIT 5
    `)
    
    console.log('\n🎮 Jogos de exemplo:')
    jogosExemplo.forEach(jogo => {
      console.log(`  ${jogo.id}: ${jogo.time_casa} vs ${jogo.time_fora} (${jogo.campeonato}) - ${jogo.data_jogo}`)
    })
    
  } catch (error) {
    console.error('❌ Erro:', error)
  }
}

testConnection()
