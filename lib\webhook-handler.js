// Handler para webhooks da API PIX
class WebhookHandler {
  constructor() {
    this.callbacks = new Map()
  }

  // Registrar callback para um tipo de evento
  on(event, callback) {
    if (!this.callbacks.has(event)) {
      this.callbacks.set(event, [])
    }
    this.callbacks.get(event).push(callback)
  }

  // Processar webhook recebido
  async processWebhook(webhookData) {
    try {
      console.log("📨 Webhook recebido:", webhookData)

      // Validar estrutura do webhook
      if (!this.validateWebhook(webhookData)) {
        throw new Error("Webhook inválido")
      }

      // Processar baseado no status
      switch (webhookData.status) {
        case "PAID":
          await this.handlePaidStatus(webhookData)
          break
        case "PENDING":
          await this.handlePendingStatus(webhookData)
          break
        case "FAILED":
          await this.handleFailedStatus(webhookData)
          break
        case "DECLINED":
          await this.handleDeclinedStatus(webhookData)
          break
        default:
          console.warn("Status desconhecido:", webhookData.status)
      }

      return { success: true, message: "Webhook processado com sucesso" }
    } catch (error) {
      console.error("Erro ao processar webhook:", error)
      return { success: false, message: error.message }
    }
  }

  // Validar estrutura do webhook
  validateWebhook(data) {
    const requiredFields = ["transaction_id", "order_id", "amount", "status", "last_updated_at"]

    return requiredFields.every((field) => data.hasOwnProperty(field))
  }

  // Processar pagamento confirmado
  async handlePaidStatus(webhookData) {
    console.log("✅ Pagamento confirmado:", webhookData.transaction_id)

    // Atualizar status no banco de dados
    await this.updatePaymentStatus(webhookData.transaction_id, "PAID")

    // Gerar bilhete
    await this.generateBilhete(webhookData)

    // Notificar callbacks
    this.emit("payment_confirmed", webhookData)
  }

  // Processar pagamento pendente
  async handlePendingStatus(webhookData) {
    console.log("⏳ Pagamento pendente:", webhookData.transaction_id)

    await this.updatePaymentStatus(webhookData.transaction_id, "PENDING")
    this.emit("payment_pending", webhookData)
  }

  // Processar pagamento falhou
  async handleFailedStatus(webhookData) {
    console.log("❌ Pagamento falhou:", webhookData.transaction_id)

    await this.updatePaymentStatus(webhookData.transaction_id, "FAILED")
    this.emit("payment_failed", webhookData)
  }

  // Processar pagamento recusado
  async handleDeclinedStatus(webhookData) {
    console.log("🚫 Pagamento recusado:", webhookData.transaction_id)

    await this.updatePaymentStatus(webhookData.transaction_id, "DECLINED")
    this.emit("payment_declined", webhookData)
  }

  // Atualizar status do pagamento
  async updatePaymentStatus(transactionId, status) {
    try {
      // Aqui seria feita a atualização no banco de dados
      console.log(`📝 Atualizando status: ${transactionId} -> ${status}`)

      // Simular atualização
      const paymentData = {
        transaction_id: transactionId,
        status: status,
        updated_at: new Date().toISOString(),
      }

      // Salvar no localStorage para simulação
      const payments = JSON.parse(localStorage.getItem("payments") || "[]")
      const existingIndex = payments.findIndex((p) => p.transaction_id === transactionId)

      if (existingIndex >= 0) {
        payments[existingIndex] = { ...payments[existingIndex], ...paymentData }
      } else {
        payments.push(paymentData)
      }

      localStorage.setItem("payments", JSON.stringify(payments))
    } catch (error) {
      console.error("Erro ao atualizar status:", error)
      throw error
    }
  }

  // Gerar bilhete após pagamento confirmado
  async generateBilhete(webhookData) {
    try {
      console.log("🎫 Gerando bilhete para:", webhookData.transaction_id)

      // Buscar dados da aposta
      const apostaData = this.getApostaData(webhookData.order_id)

      if (!apostaData) {
        throw new Error("Dados da aposta não encontrados")
      }

      // Gerar bilhete
      const bilhete = {
        id: `BLT${Date.now()}`,
        transaction_id: webhookData.transaction_id,
        order_id: webhookData.order_id,
        data: new Date().toLocaleDateString("pt-BR"),
        hora: new Date().toLocaleTimeString("pt-BR"),
        apostas: apostaData.apostas,
        valor: Number.parseFloat(webhookData.amount),
        premio_potencial: apostaData.premio_potencial || 1000.0,
        status: "ATIVO",
      }

      // Salvar bilhete
      const bilhetes = JSON.parse(localStorage.getItem("bilhetes") || "[]")
      bilhetes.push(bilhete)
      localStorage.setItem("bilhetes", JSON.stringify(bilhetes))

      // Imprimir bilhete automaticamente
      if (typeof thermalPrinter !== "undefined") {
        // @ts-ignore
        await thermalPrinter.printBilhete(bilhete)
      }

      this.emit("bilhete_generated", bilhete)

      return bilhete
    } catch (error) {
      console.error("Erro ao gerar bilhete:", error)
      throw error
    }
  }

  // Buscar dados da aposta
  getApostaData(orderId) {
    try {
      const apostas = JSON.parse(localStorage.getItem("apostas_pendentes") || "[]")
      return apostas.find((a) => a.order_id === orderId)
    } catch (error) {
      console.error("Erro ao buscar dados da aposta:", error)
      return null
    }
  }

  // Emitir evento para callbacks
  emit(event, data) {
    const callbacks = this.callbacks.get(event) || []
    callbacks.forEach((callback) => {
      try {
        callback(data)
      } catch (error) {
        console.error(`Erro no callback ${event}:`, error)
      }
    })
  }

  // Simular recebimento de webhook (para testes)
  simulateWebhook(transactionId, status = "PAID") {
    const mockWebhook = {
      qr_code_payment_id: `qr_${Date.now()}`,
      transaction_id: transactionId,
      order_id: `order_${Date.now()}`,
      amount: "25.00",
      status: status,
      end_to_end_id: new Date().toISOString(),
      last_updated_at: new Date().toISOString(),
      error: null,
    }

    return this.processWebhook(mockWebhook)
  }
}

// Instância global do webhook handler
const webhookHandler = new WebhookHandler()

// Configurar callbacks padrão
webhookHandler.on("payment_confirmed", (data) => {
  console.log("🎉 Pagamento confirmado via webhook:", data.transaction_id)

  // Notificar usuário
  if (typeof window !== "undefined") {
    // Mostrar notificação no frontend
    const event = new CustomEvent("payment_confirmed", { detail: data })
    window.dispatchEvent(event)
  }
})

webhookHandler.on("payment_failed", (data) => {
  console.log("💔 Pagamento falhou via webhook:", data.transaction_id)

  if (typeof window !== "undefined") {
    const event = new CustomEvent("payment_failed", { detail: data })
    window.dispatchEvent(event)
  }
})

webhookHandler.on("bilhete_generated", (bilhete) => {
  console.log("🎫 Bilhete gerado:", bilhete.id)

  if (typeof window !== "undefined") {
    const event = new CustomEvent("bilhete_generated", { detail: bilhete })
    window.dispatchEvent(event)
  }
})

// Exportar para uso em outros módulos
if (typeof module !== "undefined" && module.exports) {
  module.exports = { WebhookHandler, webhookHandler }
}

// Exemplo de uso
async function exemploWebhook() {
  try {
    console.log("🔄 Testando webhook handler...")

    // Simular webhook de pagamento confirmado
    const resultado = await webhookHandler.simulateWebhook("txn_123456789", "PAID")
    console.log("✅ Resultado:", resultado)
  } catch (error) {
    console.error("❌ Erro no exemplo:", error)
  }
}

// Executar exemplo se estiver em ambiente de desenvolvimento
if (typeof window !== "undefined" && window.location.hostname === "localhost") {
  // exemploWebhook()
}
