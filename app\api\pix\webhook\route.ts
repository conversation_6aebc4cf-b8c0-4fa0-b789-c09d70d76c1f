import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    console.log('📥 Webhook PIX recebido:', body)

    const {
      qr_code_payment_id,
      transaction_id,
      order_id,
      amount,
      status,
      end_to_end_id,
      last_updated_at,
      error
    } = body

    // Validar dados obrigatórios
    if (!transaction_id || !order_id || !status) {
      console.error('❌ Dados obrigatórios ausentes no webhook')
      return NextResponse.json(
        { error: 'Dados obrigatórios ausentes' },
        { status: 400 }
      )
    }

    await initializeDatabase()

    // Verificar se já existe um pagamento com este transaction_id
    const existingPayment = await executeQuery(`
      SELECT id FROM pagamentos 
      WHERE transaction_id = ? OR order_id = ?
    `, [transaction_id, order_id])

    if (existingPayment.length > 0) {
      console.log('⚠️ Pagamento já processado:', transaction_id)
      
      // Atualizar status se necessário
      await executeQuery(`
        UPDATE pagamentos 
        SET status = ?, last_updated_at = NOW()
        WHERE transaction_id = ? OR order_id = ?
      `, [status, transaction_id, order_id])
      
      return NextResponse.json({ success: true, message: 'Pagamento já processado' })
    }

    // Processar diferentes status
    switch (status) {
      case 'PAID':
        console.log('✅ Pagamento confirmado:', transaction_id)
        
        // Registrar pagamento aprovado
        await executeQuery(`
          INSERT INTO pagamentos (
            transaction_id, order_id, qr_code_payment_id,
            amount, status, end_to_end_id, 
            created_at, last_updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          transaction_id, order_id, qr_code_payment_id,
          parseFloat(amount), 'PAID', end_to_end_id
        ])

        // Aqui você pode adicionar lógica para:
        // - Confirmar apostas
        // - Enviar notificações
        // - Atualizar saldo do usuário
        
        break

      case 'PENDING':
        console.log('⏳ Pagamento pendente:', transaction_id)
        
        await executeQuery(`
          INSERT INTO pagamentos (
            transaction_id, order_id, qr_code_payment_id,
            amount, status, created_at, last_updated_at
          ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          transaction_id, order_id, qr_code_payment_id,
          parseFloat(amount), 'PENDING'
        ])
        
        break

      case 'FAILED':
      case 'DECLINED':
        console.log('❌ Pagamento falhou/recusado:', transaction_id)
        
        await executeQuery(`
          INSERT INTO pagamentos (
            transaction_id, order_id, qr_code_payment_id,
            amount, status, error_message, created_at, last_updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          transaction_id, order_id, qr_code_payment_id,
          parseFloat(amount), status, error || 'Pagamento falhou'
        ])
        
        break

      default:
        console.log('📝 Status atualizado:', status, transaction_id)
        
        await executeQuery(`
          INSERT INTO pagamentos (
            transaction_id, order_id, qr_code_payment_id,
            amount, status, created_at, last_updated_at
          ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
          ON DUPLICATE KEY UPDATE
          status = VALUES(status), last_updated_at = NOW()
        `, [
          transaction_id, order_id, qr_code_payment_id,
          parseFloat(amount), status
        ])
    }

    console.log('✅ Webhook processado com sucesso')
    
    return NextResponse.json({ 
      success: true, 
      message: 'Webhook processado com sucesso' 
    })

  } catch (error) {
    console.error('❌ Erro ao processar webhook PIX:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Permitir GET para verificação de saúde
export async function GET() {
  return NextResponse.json({ 
    status: 'ok', 
    message: 'Webhook PIX funcionando' 
  })
}
