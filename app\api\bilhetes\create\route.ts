import { NextRequest, NextResponse } from "next/server"
import { createConnection } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      user_id,
      apostas,
      valor,
      qr_code_pix,
      transaction_id,
      client_name,
      client_email,
      client_document
    } = body

    // Validações
    if (!user_id || !apostas || !Array.isArray(apostas) || apostas.length === 0) {
      return NextResponse.json({ error: "Dados inválidos" }, { status: 400 })
    }

    if (!valor || valor <= 0) {
      return NextResponse.json({ error: "Valor deve ser maior que zero" }, { status: 400 })
    }

    const connection = await createConnection()

    try {
      // Iniciar transação
      await connection.beginTransaction()

      // Gerar código único para o bilhete
      const codigo = "BLT" + Date.now().toString().slice(-6)

      // Inserir bilhete
      const [bilheteResult] = await connection.execute(`
        INSERT INTO bilhetes (
          codigo, user_id, valor, status, qr_code_pix, transaction_id,
          client_name, client_email, client_document, created_at
        ) VALUES (?, ?, ?, 'pendente', ?, ?, ?, ?, ?, NOW())
      `, [
        codigo, user_id, valor, qr_code_pix, transaction_id,
        client_name, client_email, client_document
      ])

      const bilheteId = (bilheteResult as any).insertId

      // Inserir apostas do bilhete
      for (const aposta of apostas) {
        const { match_id, resultado } = aposta
        
        await connection.execute(`
          INSERT INTO bilhete_apostas (bilhete_id, match_id, resultado, created_at)
          VALUES (?, ?, ?, NOW())
        `, [bilheteId, match_id, resultado])
      }

      // Criar pagamento pendente
      const codigoPagamento = "PAG" + Date.now().toString().slice(-6)
      
      await connection.execute(`
        INSERT INTO pagamentos (
          codigo, user_id, bilhete_id, valor, status, metodo,
          transaction_id, created_at
        ) VALUES (?, ?, ?, ?, 'pendente', 'PIX', ?, NOW())
      `, [codigoPagamento, user_id, bilheteId, valor, transaction_id])

      // Confirmar transação
      await connection.commit()

      console.log("✅ Bilhete criado com sucesso:", {
        codigo,
        user_id,
        valor,
        apostas: apostas.length,
        transaction_id
      })

      return NextResponse.json({
        success: true,
        bilhete: {
          id: codigo,
          codigo,
          valor,
          status: "pendente",
          qr_code_pix,
          transaction_id,
          apostas_count: apostas.length
        }
      })

    } catch (error) {
      // Reverter transação em caso de erro
      await connection.rollback()
      throw error
    } finally {
      await connection.end()
    }

  } catch (error) {
    console.error("❌ Erro ao criar bilhete:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
