#!/usr/bin/env node

/**
 * Script para atualizar logos dos times e campeonatos existentes
 */

import { config } from 'dotenv'
import { initializeDatabase, executeQuery } from '../lib/database-config.js'

// Carregar variáveis de ambiente
config({ path: '.env.local' })

// URLs de logos conhecidos
const LOGOS_CAMPEONATOS = {
  'Premier League': 'https://crests.football-data.org/PL.png',
  'La Liga': 'https://crests.football-data.org/PD.png',
  'Serie A': 'https://crests.football-data.org/SA.png',
  'Bundesliga': 'https://crests.football-data.org/BL1.png',
  'Ligue 1': 'https://crests.football-data.org/FL1.png',
  'UEFA Champions League': 'https://crests.football-data.org/CL.png',
  'UEFA Europa League': 'https://crests.football-data.org/EL.png',
  'Brasileirão Série A': 'https://logoeps.com/wp-content/uploads/2013/03/brasileirao-vector-logo.png'
}

const LOGOS_TIMES = {
  'Arsenal': 'https://crests.football-data.org/57.png',
  'Chelsea': 'https://crests.football-data.org/61.png',
  'Manchester United': 'https://crests.football-data.org/66.png',
  'Manchester City': 'https://crests.football-data.org/65.png',
  'Liverpool': 'https://crests.football-data.org/64.png',
  'Tottenham': 'https://crests.football-data.org/73.png',
  'Real Madrid': 'https://crests.football-data.org/86.png',
  'FC Barcelona': 'https://crests.football-data.org/81.png',
  'Atlético Madrid': 'https://crests.football-data.org/78.png',
  'Juventus': 'https://crests.football-data.org/109.png',
  'AC Milan': 'https://crests.football-data.org/98.png',
  'Inter': 'https://crests.football-data.org/108.png',
  'Bayern Munich': 'https://crests.football-data.org/5.png',
  'Borussia Dortmund': 'https://crests.football-data.org/4.png',
  'Paris Saint-Germain': 'https://crests.football-data.org/524.png',
  'Flamengo': 'https://logoeps.com/wp-content/uploads/2013/03/flamengo-vector-logo.png',
  'Palmeiras': 'https://logoeps.com/wp-content/uploads/2013/03/palmeiras-vector-logo.png',
  'Corinthians': 'https://logoeps.com/wp-content/uploads/2013/03/corinthians-vector-logo.png',
  'São Paulo': 'https://logoeps.com/wp-content/uploads/2013/03/sao-paulo-vector-logo.png',
  'Santos': 'https://logoeps.com/wp-content/uploads/2013/03/santos-vector-logo.png',
  'Fluminense': 'https://logoeps.com/wp-content/uploads/2013/03/fluminense-vector-logo.png'
}

async function updateLogos() {
  try {
    console.log('🎨 Atualizando logos...')
    
    await initializeDatabase()
    
    let updatedCampeonatos = 0
    let updatedTimes = 0
    
    // Atualizar logos dos campeonatos
    console.log('\n🏆 Atualizando logos dos campeonatos...')
    for (const [nome, logoUrl] of Object.entries(LOGOS_CAMPEONATOS)) {
      try {
        const result = await executeQuery(`
          UPDATE campeonatos 
          SET logo_url = ? 
          WHERE nome LIKE ? AND (logo_url IS NULL OR logo_url = '')
        `, [logoUrl, `%${nome}%`])
        
        if (result.changes > 0) {
          console.log(`✅ ${nome}: ${result.changes} registros atualizados`)
          updatedCampeonatos += result.changes
        }
      } catch (error) {
        console.error(`❌ Erro ao atualizar ${nome}:`, error.message)
      }
    }
    
    // Atualizar logos dos times
    console.log('\n👥 Atualizando logos dos times...')
    for (const [nome, logoUrl] of Object.entries(LOGOS_TIMES)) {
      try {
        const result = await executeQuery(`
          UPDATE times 
          SET logo_url = ? 
          WHERE nome LIKE ? AND (logo_url IS NULL OR logo_url = '')
        `, [logoUrl, `%${nome}%`])
        
        if (result.changes > 0) {
          console.log(`✅ ${nome}: ${result.changes} registros atualizados`)
          updatedTimes += result.changes
        }
      } catch (error) {
        console.error(`❌ Erro ao atualizar ${nome}:`, error.message)
      }
    }
    
    console.log('\n📊 RESUMO:')
    console.log(`Campeonatos atualizados: ${updatedCampeonatos}`)
    console.log(`Times atualizados: ${updatedTimes}`)
    
    // Verificar estatísticas finais
    const statsCampeonatos = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(logo_url) as com_logo
      FROM campeonatos 
      WHERE status = 'ativo'
    `)
    
    const statsTimes = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(logo_url) as com_logo
      FROM times
    `)
    
    console.log('\n📈 ESTATÍSTICAS FINAIS:')
    console.log(`Campeonatos: ${statsCampeonatos[0].com_logo}/${statsCampeonatos[0].total} com logo`)
    console.log(`Times: ${statsTimes[0].com_logo}/${statsTimes[0].total} com logo`)
    
    return {
      campeonatos: updatedCampeonatos,
      times: updatedTimes
    }
    
  } catch (error) {
    console.error('❌ Erro:', error)
    throw error
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  updateLogos().then((result) => {
    console.log(`✅ Processo finalizado! ${result.campeonatos + result.times} logos atualizados.`)
    process.exit(0)
  }).catch(error => {
    console.error('💥 Processo finalizado com erro:', error.message)
    process.exit(1)
  })
}

export { updateLogos }
