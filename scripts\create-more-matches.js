#!/usr/bin/env node

/**
 * Script para criar mais partidas mock para teste
 */

import { config } from 'dotenv'
import { initializeDatabase, executeQuery, executeQuerySingle } from '../lib/database-config.js'

// Carregar variáveis de ambiente
config({ path: '.env.local' })

async function createMoreMatches() {
  try {
    console.log('🚀 Criando mais partidas mock...')
    
    await initializeDatabase()
    
    // Buscar todos os campeonatos ativos
    const campeonatos = await executeQuery(`
      SELECT id, nome FROM campeonatos 
      WHERE status = 'ativo'
      ORDER BY id
    `)
    
    // Buscar todos os times
    const times = await executeQuery(`
      SELECT id, nome FROM times 
      ORDER BY id
    `)
    
    console.log(`📊 Encontrados ${campeonatos.length} campeonatos e ${times.length} times`)
    
    let totalPartidas = 0
    
    // Para cada campeonato, criar várias partidas
    for (const campeonato of campeonatos) {
      console.log(`🏆 Criando partidas para ${campeonato.nome}...`)
      
      // Criar 10 partidas por campeonato
      for (let i = 0; i < 10; i++) {
        try {
          // Selecionar times aleatórios
          const timeCasa = times[Math.floor(Math.random() * times.length)]
          const timeFora = times[Math.floor(Math.random() * times.length)]
          
          // Evitar time jogando contra si mesmo
          if (timeCasa.id === timeFora.id) continue
          
          // Data da partida (próximos 30 dias)
          const dataJogo = new Date()
          dataJogo.setDate(dataJogo.getDate() + Math.floor(Math.random() * 30) + 1)
          dataJogo.setHours(15 + Math.floor(Math.random() * 6), 0, 0, 0) // Entre 15h e 21h
          
          const apiId = `mock_${campeonato.id}_${timeCasa.id}_${timeFora.id}_${i}`
          
          // Verificar se já existe
          const existing = await executeQuerySingle(
            'SELECT id FROM jogos WHERE api_id = ?',
            [apiId]
          )
          
          if (!existing) {
            await executeQuery(`
              INSERT INTO jogos (
                campeonato_id, time_casa_id, time_fora_id, 
                data_jogo, rodada, status, api_id
              ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
              campeonato.id,
              timeCasa.id,
              timeFora.id,
              dataJogo.toISOString(),
              i + 1,
              'agendado',
              apiId
            ])
            
            totalPartidas++
          }
          
        } catch (error) {
          console.error(`❌ Erro ao criar partida ${i + 1}:`, error.message)
        }
      }
    }
    
    console.log(`✅ Criadas ${totalPartidas} partidas mock`)
    
    // Verificar total de partidas no banco
    const total = await executeQuerySingle('SELECT COUNT(*) as total FROM jogos')
    console.log(`📊 Total de partidas no banco: ${total.total}`)
    
    return totalPartidas
    
  } catch (error) {
    console.error('❌ Erro ao criar partidas:', error)
    throw error
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  createMoreMatches().then((total) => {
    console.log(`✅ Processo finalizado! ${total} partidas criadas.`)
    process.exit(0)
  }).catch(error => {
    console.error('💥 Processo finalizado com erro:', error.message)
    process.exit(1)
  })
}

export { createMoreMatches }
