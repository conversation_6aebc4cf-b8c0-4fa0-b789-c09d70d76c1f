import mysql from 'mysql2/promise'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

// Configuração da API Football-data.org
const FOOTBALL_API_BASE = 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN

const headers = {
  'X-Auth-Token': FOOTBALL_API_TOKEN,
  'Content-Type': 'application/json'
}

// Configuração do MySQL
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4'
}

let connection

/**
 * Conecta ao MySQL
 */
async function connectDatabase() {
  try {
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Conectado ao MySQL')
    return connection
  } catch (error) {
    console.error('❌ Erro ao conectar ao MySQL:', error.message)
    throw error
  }
}

/**
 * Busca dados da API Football-data.org
 */
async function fetchFootballData(endpoint) {
  try {
    console.log(`🌐 Buscando: ${FOOTBALL_API_BASE}${endpoint}`)
    
    const response = await fetch(`${FOOTBALL_API_BASE}${endpoint}`, {
      headers,
      timeout: 15000
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error.message)
    return null
  }
}

/**
 * Sincroniza competições
 */
async function syncCompetitions() {
  try {
    console.log('🏆 Sincronizando competições...')
    
    const data = await fetchFootballData('/competitions')
    if (!data || !data.competitions) {
      console.log('❌ Nenhuma competição encontrada')
      return 0
    }
    
    const competitions = data.competitions
    let syncCount = 0
    
    for (const comp of competitions) {
      try {
        // Verificar se já existe
        const [existing] = await connection.execute(
          'SELECT id FROM campeonatos WHERE api_id = ?',
          [comp.id]
        )
        
        if (existing.length === 0) {
          // Inserir nova competição
          await connection.execute(`
            INSERT INTO campeonatos (
              nome, descricao, pais, temporada, status, data_inicio, data_fim,
              logo_url, api_id, codigo, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
          `, [
            comp.name,
            comp.name,
            comp.area?.name || 'Internacional',
            new Date().getFullYear(),
            'ativo',
            comp.currentSeason?.startDate || new Date().toISOString().split('T')[0],
            comp.currentSeason?.endDate || new Date(Date.now() + 365*24*60*60*1000).toISOString().split('T')[0],
            comp.emblem || null,
            comp.id,
            comp.code
          ])
          
          console.log(`✅ Competição criada: ${comp.name}`)
          syncCount++
        }
        
        // Delay para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`❌ Erro ao processar competição ${comp.name}:`, error.message)
      }
    }
    
    console.log(`🏆 ${syncCount} competições sincronizadas`)
    return syncCount
    
  } catch (error) {
    console.error('❌ Erro na sincronização de competições:', error.message)
    return 0
  }
}

/**
 * Sincroniza times de uma competição
 */
async function syncTeamsForCompetition(competitionApiId, competitionDbId) {
  try {
    console.log(`👥 Sincronizando times da competição ${competitionApiId}...`)
    
    const data = await fetchFootballData(`/competitions/${competitionApiId}/teams`)
    if (!data || !data.teams) {
      console.log(`❌ Nenhum time encontrado para competição ${competitionApiId}`)
      return 0
    }
    
    const teams = data.teams
    let syncCount = 0
    
    for (const team of teams) {
      try {
        // Verificar se já existe
        const [existing] = await connection.execute(
          'SELECT id FROM times WHERE api_id = ?',
          [team.id]
        )
        
        if (existing.length === 0) {
          // Inserir novo time
          await connection.execute(`
            INSERT INTO times (
              nome, nome_curto, pais, logo_url, api_id, codigo, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, NOW())
          `, [
            team.name,
            team.shortName || team.tla || team.name,
            team.area?.name || 'Desconhecido',
            team.crest || null,
            team.id,
            team.tla
          ])
          
          console.log(`✅ Time criado: ${team.name}`)
          syncCount++
        }
        
        // Delay para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`❌ Erro ao processar time ${team.name}:`, error.message)
      }
    }
    
    console.log(`👥 ${syncCount} times sincronizados para competição ${competitionApiId}`)
    return syncCount
    
  } catch (error) {
    console.error(`❌ Erro na sincronização de times da competição ${competitionApiId}:`, error.message)
    return 0
  }
}

/**
 * Sincroniza partidas de uma competição
 */
async function syncMatchesForCompetition(competitionApiId) {
  try {
    console.log(`⚽ Sincronizando partidas da competição ${competitionApiId}...`)
    
    const data = await fetchFootballData(`/competitions/${competitionApiId}/matches`)
    if (!data || !data.matches) {
      console.log(`❌ Nenhuma partida encontrada para competição ${competitionApiId}`)
      return 0
    }
    
    const matches = data.matches
    let syncCount = 0
    
    for (const match of matches) {
      try {
        // Verificar se já existe
        const [existing] = await connection.execute(
          'SELECT id FROM jogos WHERE api_id = ?',
          [match.id]
        )
        
        if (existing.length > 0) {
          continue // Pular se já existe
        }
        
        // Buscar IDs dos times no banco local
        const [homeTeam] = await connection.execute(
          'SELECT id FROM times WHERE api_id = ?',
          [match.homeTeam.id]
        )
        
        const [awayTeam] = await connection.execute(
          'SELECT id FROM times WHERE api_id = ?',
          [match.awayTeam.id]
        )
        
        // Buscar ID da competição no banco local
        const [competition] = await connection.execute(
          'SELECT id FROM campeonatos WHERE api_id = ?',
          [competitionApiId]
        )
        
        if (homeTeam.length === 0 || awayTeam.length === 0 || competition.length === 0) {
          console.log(`⚠️ Dados incompletos para partida ${match.id}, pulando...`)
          continue
        }
        
        // Inserir nova partida
        await connection.execute(`
          INSERT INTO jogos (
            campeonato_id, time_casa_id, time_fora_id, data_jogo, local_jogo,
            rodada, resultado_casa, resultado_fora, status, api_id, data_criacao
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        `, [
          competition[0].id,
          homeTeam[0].id,
          awayTeam[0].id,
          match.utcDate,
          match.venue || null,
          match.matchday || null,
          match.score?.fullTime?.home || null,
          match.score?.fullTime?.away || null,
          match.status === 'FINISHED' ? 'finalizado' : 
          match.status === 'IN_PLAY' ? 'ao_vivo' : 'agendado',
          match.id
        ])
        
        console.log(`✅ Partida criada: ${match.homeTeam.shortName} vs ${match.awayTeam.shortName}`)
        syncCount++
        
        // Delay para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`❌ Erro ao processar partida ${match.id}:`, error.message)
      }
    }
    
    console.log(`⚽ ${syncCount} partidas sincronizadas para competição ${competitionApiId}`)
    return syncCount
    
  } catch (error) {
    console.error(`❌ Erro na sincronização de partidas da competição ${competitionApiId}:`, error.message)
    return 0
  }
}

/**
 * Execução principal
 */
async function main() {
  try {
    console.log('🚀 Iniciando sincronização completa...')
    
    await connectDatabase()
    
    // 1. Sincronizar competições
    const competitionsCount = await syncCompetitions()
    console.log(`🏆 Total de competições: ${competitionsCount}`)
    
    // 2. Buscar competições ativas para sincronizar times e partidas
    const [competitions] = await connection.execute(`
      SELECT id, api_id, nome FROM campeonatos 
      WHERE status = 'ativo' AND api_id IS NOT NULL
      ORDER BY id
    `)
    
    console.log(`📋 Encontradas ${competitions.length} competições para sincronizar`)
    
    let totalTeams = 0
    let totalMatches = 0
    
    // 3. Sincronizar times e partidas para cada competição
    for (const comp of competitions) {
      console.log(`\n🎯 Processando: ${comp.nome} (API ID: ${comp.api_id})`)
      
      // Sincronizar times
      const teamsCount = await syncTeamsForCompetition(comp.api_id, comp.id)
      totalTeams += teamsCount
      
      // Delay entre operações
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Sincronizar partidas
      const matchesCount = await syncMatchesForCompetition(comp.api_id)
      totalMatches += matchesCount
      
      // Delay entre competições
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
    
    console.log('\n🎉 Sincronização completa!')
    console.log(`📊 Resumo:`)
    console.log(`   🏆 Competições: ${competitionsCount}`)
    console.log(`   👥 Times: ${totalTeams}`)
    console.log(`   ⚽ Partidas: ${totalMatches}`)
    
  } catch (error) {
    console.error('❌ Erro na sincronização:', error.message)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Conexão fechada')
    }
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { syncCompetitions, syncTeamsForCompetition, syncMatchesForCompetition }
