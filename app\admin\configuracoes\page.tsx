"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Settings, Save, Database, Mail, Shield } from "lucide-react"
import { toast } from "sonner"

export default function ConfiguracoesPage() {
  const [pixConfig, setPixConfig] = useState({
    baseURL: "https://api.meiodepagamento.com/api/V1/Transacao",
    token:
      "Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w==",
    timeout: 15,
    commission: 10,
    autoConfirm: true,
  })

  const handleSavePixConfig = () => {
    // Salvar configurações PIX
    localStorage.setItem("pix_config", JSON.stringify(pixConfig))
    toast.success("Configurações PIX salvas com sucesso!")
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Configurações</h1>
        <p className="text-gray-600 mt-2">Configure as opções do sistema</p>
      </div>

      <Tabs defaultValue="geral" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="geral">Geral</TabsTrigger>
          <TabsTrigger value="pagamentos">Pagamentos</TabsTrigger>
          <TabsTrigger value="notificacoes">Notificações</TabsTrigger>
          <TabsTrigger value="seguranca">Segurança</TabsTrigger>
        </TabsList>

        <TabsContent value="geral">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Configurações Gerais
              </CardTitle>
              <CardDescription>Configure as opções básicas do sistema</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="site-name">Nome do Site</Label>
                    <Input id="site-name" defaultValue="Sistema Bolão" />
                  </div>
                  <div>
                    <Label htmlFor="site-description">Descrição</Label>
                    <Textarea id="site-description" defaultValue="Sistema de apostas esportivas" />
                  </div>
                  <div>
                    <Label htmlFor="contact-email">Email de Contato</Label>
                    <Input id="contact-email" type="email" defaultValue="<EMAIL>" />
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="min-bet">Valor Mínimo da Aposta (R$)</Label>
                    <Input id="min-bet" type="number" defaultValue="5.00" />
                  </div>
                  <div>
                    <Label htmlFor="max-bet">Valor Máximo da Aposta (R$)</Label>
                    <Input id="max-bet" type="number" defaultValue="1000.00" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="maintenance">Modo Manutenção</Label>
                      <p className="text-sm text-gray-600">Ativar modo de manutenção do site</p>
                    </div>
                    <Switch id="maintenance" />
                  </div>
                </div>
              </div>
              <div className="flex justify-end">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Configurações
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pagamentos">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Configurações de Pagamento PIX
              </CardTitle>
              <CardDescription>Configure as opções de pagamento PIX conforme documentação oficial</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="pix-api-url">PIX API Base URL</Label>
                  <Input
                    id="pix-api-url"
                    value={pixConfig.baseURL}
                    onChange={(e) => setPixConfig({ ...pixConfig, baseURL: e.target.value })}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Endpoint oficial: https://api.meiodepagamento.com/api/V1/Transacao
                  </p>
                </div>
                <div>
                  <Label htmlFor="pix-token">PIX API Token</Label>
                  <Textarea
                    id="pix-token"
                    value={pixConfig.token}
                    onChange={(e) => setPixConfig({ ...pixConfig, token: e.target.value })}
                    className="font-mono text-xs"
                    rows={3}
                  />
                  <p className="text-xs text-gray-500 mt-1">Token fornecido pela instituição financeira</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="pix-timeout">Timeout PIX (minutos)</Label>
                    <Input
                      id="pix-timeout"
                      type="number"
                      value={pixConfig.timeout}
                      onChange={(e) => setPixConfig({ ...pixConfig, timeout: Number.parseInt(e.target.value) })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="commission">Comissão Cambista (%)</Label>
                    <Input
                      id="commission"
                      type="number"
                      value={pixConfig.commission}
                      onChange={(e) => setPixConfig({ ...pixConfig, commission: Number.parseInt(e.target.value) })}
                    />
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-confirm">Confirmação Automática</Label>
                    <p className="text-sm text-gray-600">Confirmar pagamentos automaticamente via webhook</p>
                  </div>
                  <Switch
                    id="auto-confirm"
                    checked={pixConfig.autoConfirm}
                    onCheckedChange={(checked) => setPixConfig({ ...pixConfig, autoConfirm: checked })}
                  />
                </div>
              </div>

              {/* Status da Integração PIX */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-800 mb-2">Status da Integração PIX</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-green-700">Endpoint SolicitacaoQRCode:</span>
                    <span className="text-green-600 font-medium">✅ Configurado</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700">Token de Autenticação:</span>
                    <span className="text-green-600 font-medium">✅ Válido</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700">Webhook Handler:</span>
                    <span className="text-green-600 font-medium">✅ Ativo</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSavePixConfig}>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Configurações PIX
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notificacoes">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="h-5 w-5 mr-2" />
                Configurações de Notificações
              </CardTitle>
              <CardDescription>Configure as notificações do sistema</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email-notifications">Notificações por Email</Label>
                    <p className="text-sm text-gray-600">Enviar notificações por email</p>
                  </div>
                  <Switch id="email-notifications" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sms-notifications">Notificações por SMS</Label>
                    <p className="text-sm text-gray-600">Enviar notificações por SMS</p>
                  </div>
                  <Switch id="sms-notifications" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="push-notifications">Notificações Push</Label>
                    <p className="text-sm text-gray-600">Enviar notificações push</p>
                  </div>
                  <Switch id="push-notifications" defaultChecked />
                </div>
                <div>
                  <Label htmlFor="smtp-server">Servidor SMTP</Label>
                  <Input id="smtp-server" defaultValue="smtp.gmail.com" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="smtp-user">Usuário SMTP</Label>
                    <Input id="smtp-user" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div>
                    <Label htmlFor="smtp-password">Senha SMTP</Label>
                    <Input id="smtp-password" type="password" />
                  </div>
                </div>
              </div>
              <div className="flex justify-end">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Configurações
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seguranca">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Configurações de Segurança
              </CardTitle>
              <CardDescription>Configure as opções de segurança do sistema</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="two-factor">Autenticação de Dois Fatores</Label>
                    <p className="text-sm text-gray-600">Exigir 2FA para administradores</p>
                  </div>
                  <Switch id="two-factor" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="login-attempts">Limitar Tentativas de Login</Label>
                    <p className="text-sm text-gray-600">Bloquear após tentativas falhadas</p>
                  </div>
                  <Switch id="login-attempts" defaultChecked />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="max-attempts">Máximo de Tentativas</Label>
                    <Input id="max-attempts" type="number" defaultValue="5" />
                  </div>
                  <div>
                    <Label htmlFor="lockout-time">Tempo de Bloqueio (minutos)</Label>
                    <Input id="lockout-time" type="number" defaultValue="30" />
                  </div>
                </div>
                <div>
                  <Label htmlFor="session-timeout">Timeout de Sessão (horas)</Label>
                  <Input id="session-timeout" type="number" defaultValue="24" />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="audit-log">Log de Auditoria</Label>
                    <p className="text-sm text-gray-600">Registrar todas as ações administrativas</p>
                  </div>
                  <Switch id="audit-log" defaultChecked />
                </div>
              </div>
              <div className="flex justify-end">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Configurações
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
