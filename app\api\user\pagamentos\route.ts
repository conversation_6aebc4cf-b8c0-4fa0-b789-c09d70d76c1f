import { NextRequest, NextResponse } from "next/server"
import { createConnection } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json({ error: "user_id é obrigatório" }, { status: 400 })
    }

    const connection = await createConnection()

    // Buscar pagamentos do usuário
    const [pagamentos] = await connection.execute(`
      SELECT 
        p.id,
        p.codigo,
        p.valor,
        p.status,
        p.metodo,
        p.created_at,
        b.codigo as bilhete_codigo
      FROM pagamentos p
      LEFT JOIN bilhetes b ON p.bilhete_id = b.id
      WHERE p.user_id = ?
      ORDER BY p.created_at DESC
    `, [userId])

    // Formatar os dados para o frontend
    const pagamentosFormatados = (pagamentos as any[]).map(pagamento => ({
      id: pagamento.codigo,
      data: new Date(pagamento.created_at).toLocaleString('pt-BR'),
      valor: parseFloat(pagamento.valor),
      status: pagamento.status,
      metodo: pagamento.metodo,
      bilhete_id: pagamento.bilhete_codigo
    }))

    await connection.end()

    return NextResponse.json({
      success: true,
      pagamentos: pagamentosFormatados
    })

  } catch (error) {
    console.error("❌ Erro ao buscar pagamentos:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
