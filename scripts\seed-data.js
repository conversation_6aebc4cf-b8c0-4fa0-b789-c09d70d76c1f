// Script para popular o banco de dados com dados de exemplo
// Execute após criar as tabelas com database-setup.sql

const sqlite3 = require("sqlite3").verbose()
const path = require("path")

// Conectar ao banco de dados
const dbPath = path.join(__dirname, "..", "database", "sistema-bolao.sqlite")
const db = new sqlite3.Database(dbPath)

console.log("🌱 Iniciando população do banco de dados...")

// Função para executar queries com Promise
function runQuery(query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) {
        reject(err)
      } else {
        resolve(this)
      }
    })
  })
}

// Função para buscar dados
function getQuery(query, params = []) {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) {
        reject(err)
      } else {
        resolve(rows)
      }
    })
  })
}

async function seedDatabase() {
  try {
    // Limpar dados existentes (exceto configurações e usuário admin)
    console.log("🧹 Limpando dados existentes...")
    await runQuery("DELETE FROM aposta_detalhes")
    await runQuery("DELETE FROM apostas")
    await runQuery("DELETE FROM premios")
    await runQuery("DELETE FROM pagamentos")
    await runQuery("DELETE FROM bolao_jogos")
    await runQuery("DELETE FROM boloes")
    await runQuery("DELETE FROM jogos")

    // Buscar IDs dos campeonatos e times
    const campeonatos = await getQuery("SELECT * FROM campeonatos")
    const times = await getQuery("SELECT * FROM times")
    const admin = await getQuery('SELECT * FROM usuarios WHERE tipo = "admin" LIMIT 1')

    if (campeonatos.length === 0 || times.length === 0 || admin.length === 0) {
      throw new Error("Dados básicos não encontrados. Execute primeiro o database-setup.sql")
    }

    const brasileirao = campeonatos.find((c) => c.nome.includes("Brasileirão"))
    const copaBrasil = campeonatos.find((c) => c.nome.includes("Copa do Brasil"))

    // Criar jogos de exemplo
    console.log("⚽ Criando jogos de exemplo...")

    const jogosData = [
      // Brasileirão
      {
        campeonato_id: brasileirao.id,
        time_casa: "Flamengo",
        time_fora: "Palmeiras",
        data_jogo: "2024-01-20 16:00:00",
        rodada: 1,
      },
      {
        campeonato_id: brasileirao.id,
        time_casa: "São Paulo",
        time_fora: "Corinthians",
        data_jogo: "2024-01-21 18:30:00",
        rodada: 1,
      },
      {
        campeonato_id: brasileirao.id,
        time_casa: "Atlético-MG",
        time_fora: "Cruzeiro",
        data_jogo: "2024-01-22 20:00:00",
        rodada: 1,
      },
      {
        campeonato_id: brasileirao.id,
        time_casa: "Grêmio",
        time_fora: "Internacional",
        data_jogo: "2024-01-23 21:00:00",
        rodada: 1,
      },
      {
        campeonato_id: brasileirao.id,
        time_casa: "Botafogo",
        time_fora: "Vasco",
        data_jogo: "2024-01-24 19:00:00",
        rodada: 1,
      },
      {
        campeonato_id: brasileirao.id,
        time_casa: "Fluminense",
        time_fora: "Santos",
        data_jogo: "2024-01-25 20:30:00",
        rodada: 1,
      },
      // Copa do Brasil
      {
        campeonato_id: copaBrasil.id,
        time_casa: "Palmeiras",
        time_fora: "São Paulo",
        data_jogo: "2024-01-26 21:30:00",
        rodada: 1,
      },
      {
        campeonato_id: copaBrasil.id,
        time_casa: "Flamengo",
        time_fora: "Botafogo",
        data_jogo: "2024-01-27 16:00:00",
        rodada: 1,
      },
    ]

    // Inserir jogos no banco de dados
    for (const jogo of jogosData) {
      await runQuery(
        "INSERT INTO jogos (campeonato_id, time_casa, time_fora, data_jogo, rodada) VALUES (?, ?, ?, ?, ?)",
        [jogo.campeonato_id, jogo.time_casa, jogo.time_fora, jogo.data_jogo, jogo.rodada],
      )
    }

    console.log("✅ Banco de dados populado com sucesso!")
  } catch (error) {
    console.error("❌ Erro ao popular o banco de dados:", error)
  } finally {
    db.close()
  }
}

seedDatabase()
