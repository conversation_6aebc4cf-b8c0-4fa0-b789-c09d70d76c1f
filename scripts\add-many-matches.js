import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function createManyMatches() {
  try {
    console.log('🚀 Criando muitas partidas...')
    
    await initializeDatabase()
    
    // Buscar todos os campeonatos
    const campeonatos = await executeQuery(`
      SELECT id, nome FROM campeonatos 
      WHERE status = 'ativo'
      ORDER BY id
    `)
    
    // Buscar todos os times
    const times = await executeQuery(`
      SELECT id, nome, nome_curto FROM times 
      ORDER BY id
    `)
    
    console.log(`📊 Encontrados ${campeonatos.length} campeonatos e ${times.length} times`)
    
    let totalPartidas = 0
    
    // Para cada campeonato, criar muitas partidas
    for (const campeonato of campeonatos) {
      console.log(`\n🏆 Criando partidas para: ${campeonato.nome}`)
      
      // Criar 50 partidas por campeonato
      for (let i = 0; i < 50; i++) {
        try {
          // Selecionar dois times aleatórios diferentes
          const timeCasa = times[Math.floor(Math.random() * times.length)]
          let timeFora = times[Math.floor(Math.random() * times.length)]
          
          // Garantir que os times sejam diferentes
          while (timeFora.id === timeCasa.id) {
            timeFora = times[Math.floor(Math.random() * times.length)]
          }
          
          // Data aleatória nos próximos 60 dias
          const dataJogo = new Date()
          dataJogo.setDate(dataJogo.getDate() + Math.floor(Math.random() * 60))
          
          // Rodada aleatória
          const rodada = Math.floor(Math.random() * 38) + 1
          
          // Inserir partida
          await executeQuery(`
            INSERT INTO jogos (
              campeonato_id, time_casa_id, time_fora_id, data_jogo, 
              rodada, status, data_criacao
            ) VALUES (?, ?, ?, ?, ?, 'agendado', NOW())
          `, [
            campeonato.id,
            timeCasa.id,
            timeFora.id,
            dataJogo.toISOString(),
            rodada
          ])
          
          totalPartidas++
          
          if (i % 10 === 0) {
            console.log(`   ✅ ${i + 1}/50 partidas criadas para ${campeonato.nome}`)
          }
          
        } catch (error) {
          console.error(`❌ Erro ao criar partida ${i + 1}:`, error.message)
        }
      }
      
      console.log(`✅ 50 partidas criadas para ${campeonato.nome}`)
    }
    
    console.log(`\n🎉 Total de partidas criadas: ${totalPartidas}`)
    
    // Verificar total de partidas no banco
    const [result] = await executeQuery(`
      SELECT COUNT(*) as total FROM jogos WHERE status = 'agendado'
    `)
    
    console.log(`📊 Total de partidas agendadas no banco: ${result.total}`)
    
  } catch (error) {
    console.error('❌ Erro ao criar partidas:', error.message)
  }
}

// Executar
createManyMatches()
