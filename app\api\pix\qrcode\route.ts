import { NextRequest, NextResponse } from 'next/server'
import QRCode from 'qrcode'

const PIX_API_BASE_URL = 'https://api.meiodepagamento.com/api/V1/Transacao'
const PIX_TOKEN = 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='

export async function POST(request: NextRequest) {
  try {
    let body
    try {
      body = await request.json()
    } catch (parseError) {
      console.error('❌ Erro ao parsear JSON da requisição:', parseError)
      return NextResponse.json(
        { error: 'JSON inválido na requisição' },
        { status: 400 }
      )
    }
    
    const {
      value,
      description = 'Pagamento de aposta',
      client_name,
      client_email,
      client_document,
      qrcode_image = false
    } = body

    // Validações
    if (!value || value <= 0) {
      return NextResponse.json(
        { error: 'Valor deve ser maior que zero' },
        { status: 400 }
      )
    }

    if (!client_name || !client_email || !client_document) {
      return NextResponse.json(
        { error: 'Nome, email e documento do cliente são obrigatórios' },
        { status: 400 }
      )
    }

    // Preparar dados para a API do PIX
    const pixData = {
      token: PIX_TOKEN,
      value: parseFloat(value.toString()),
      description,
      client_name,
      client_email,
      client_document,
      qrcode_image
    }

    console.log('🔄 Solicitando QR Code PIX:', {
      value: pixData.value,
      client_name: pixData.client_name,
      client_email: pixData.client_email
    })

    // Fazer requisição para a API do PIX
    const response = await fetch(`${PIX_API_BASE_URL}/SolicitacaoQRCode`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pixData)
    })

    const responseText = await response.text()
    
    if (!response.ok) {
      console.error('❌ Erro na API PIX:', response.status, responseText)
      return NextResponse.json(
        { error: 'Erro ao gerar QR Code PIX: ' + responseText },
        { status: response.status }
      )
    }

    let pixResponse
    try {
      pixResponse = JSON.parse(responseText)
    } catch (parseError) {
      console.error('❌ Erro ao parsear resposta PIX:', responseText)
      return NextResponse.json(
        { error: 'Resposta inválida da API PIX' },
        { status: 500 }
      )
    }

    // Verificar se houve erro na resposta
    if (typeof pixResponse === 'string') {
      console.error('❌ Erro retornado pela API PIX:', pixResponse)
      return NextResponse.json(
        { error: pixResponse },
        { status: 400 }
      )
    }

    console.log('✅ QR Code PIX gerado com sucesso:', {
      transaction_id: pixResponse.transaction_id,
      order_id: pixResponse.order_id,
      status: pixResponse.status
    })

    // Gerar QR Code visual se não veio da API
    let qrCodeImage = pixResponse.qrcode_image
    if (!qrCodeImage && pixResponse.qr_code_value) {
      try {
        qrCodeImage = await QRCode.toDataURL(pixResponse.qr_code_value, {
          width: 300,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })
        console.log('✅ QR Code visual gerado localmente')
      } catch (qrError) {
        console.error('❌ Erro ao gerar QR Code visual:', qrError)
      }
    }

    // Retornar resposta formatada
    return NextResponse.json({
      success: true,
      data: {
        qr_code_value: pixResponse.qr_code_value,
        qrcode_image: qrCodeImage,
        expiration_datetime: pixResponse.expiration_datetime,
        status: pixResponse.status,
        transaction_id: pixResponse.transaction_id,
        order_id: pixResponse.order_id,
        value: pixData.value,
        client_name: pixData.client_name,
        client_email: pixData.client_email
      }
    })

  } catch (error) {
    console.error('❌ Erro interno ao gerar QR Code PIX:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
