import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    await initializeDatabase()

    // Primeiro, verificar se há bolões na tabela
    const totalBoloes = await executeQuery(`SELECT COUNT(*) as total FROM boloes`)
    console.log("🔍 Total de bolões na tabela:", totalBoloes[0]?.total || 0)

    // Buscar bolões ativos
    console.log("🔍 Buscando bolões ativos...")
    const boloes = await executeQuery(`
      SELECT
        b.*,
        u.nome as criado_por_nome
      FROM boloes b
      LEFT JOIN usuarios u ON b.criado_por = u.id
      WHERE b.status = 'ativo'
      ORDER BY b.data_criacao DESC
    `)

    console.log(`📊 Encontrados ${boloes.length} bolões:`, boloes.map(b => ({ id: b.id, nome: b.nome, status: b.status })))

    // Para cada bolão, buscar os jogos reais das partidas selecionadas
    const boloesComJogos = await Promise.all(boloes.map(async (bolao) => {
      let jogos = []

      try {
        // Tentar buscar jogos reais associados ao bolão através da tabela bolao_jogos (MySQL)
        try {
          const jogosQuery = `
            SELECT
              j.*,
              c.nome as campeonato_nome,
              c.descricao as campeonato_descricao,
              c.logo_url as campeonato_logo,
              c.pais as campeonato_pais,
              tc.nome as time_casa_nome,
              tc.nome_curto as time_casa_curto,
              tc.logo_url as time_casa_logo,
              tc.pais as time_casa_pais,
              tf.nome as time_fora_nome,
              tf.nome_curto as time_fora_curto,
              tf.logo_url as time_fora_logo,
              tf.pais as time_fora_pais
            FROM bolao_jogos bj
            JOIN jogos j ON bj.jogo_id = j.id
            LEFT JOIN campeonatos c ON j.campeonato_id = c.id
            LEFT JOIN times tc ON j.time_casa_id = tc.id
            LEFT JOIN times tf ON j.time_fora_id = tf.id
            WHERE bj.bolao_id = ?
            ORDER BY j.data_jogo ASC
          `

          jogos = await executeQuery(jogosQuery, [bolao.id])
        } catch (error) {
          console.log(`⚠️ Erro ao buscar jogos via bolao_jogos (MySQL): ${error.message}`)
          jogos = []
        }

        // Se não encontrou jogos na tabela bolao_jogos, tentar buscar pelas partidas_selecionadas
        if (jogos.length === 0 && bolao.partidas_selecionadas) {
          const partidasIds = typeof bolao.partidas_selecionadas === 'string'
            ? JSON.parse(bolao.partidas_selecionadas)
            : bolao.partidas_selecionadas

          if (Array.isArray(partidasIds) && partidasIds.length > 0) {
            // Extrair apenas os IDs se são objetos
            const ids = partidasIds.map(p => typeof p === 'object' && p !== null ? p.id : p).filter(id => id)

            if (ids.length > 0) {
              const placeholders = ids.map(() => '?').join(',')

              // Buscar jogos no MySQL
              try {
                const jogosAlternativosQuery = `
                  SELECT
                    j.*,
                    c.nome as campeonato_nome,
                    c.descricao as campeonato_descricao,
                    c.logo_url as campeonato_logo,
                    c.pais as campeonato_pais,
                    tc.nome as time_casa_nome,
                    tc.nome_curto as time_casa_curto,
                    tc.logo_url as time_casa_logo,
                    tc.pais as time_casa_pais,
                    tf.nome as time_fora_nome,
                    tf.nome_curto as time_fora_curto,
                    tf.logo_url as time_fora_logo,
                    tf.pais as time_fora_pais
                  FROM jogos j
                  LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                  LEFT JOIN times tc ON j.time_casa_id = tc.id
                  LEFT JOIN times tf ON j.time_fora_id = tf.id
                  WHERE j.id IN (${placeholders})
                  ORDER BY j.data_jogo ASC
                `

                jogos = await executeQuery(jogosAlternativosQuery, ids)
                console.log(`🎮 Bolão ${bolao.id}: Encontrados ${jogos.length} jogos no MySQL`)
              } catch (mysqlError) {
                console.log(`⚠️ Erro ao buscar jogos no MySQL: ${mysqlError.message}`)
                jogos = []
              }
            }
          }
        }

        console.log(`🎮 Bolão ${bolao.id} (${bolao.nome}): ${jogos.length} jogos encontrados`)

      } catch (error) {
        console.error(`Erro ao buscar jogos do bolão ${bolao.id}:`, error)
        jogos = []
      }

      return {
        ...bolao,
        jogos: jogos
      }
    }))

    console.log(`✅ Retornando ${boloesComJogos.length} bolões com jogos`)

    return NextResponse.json({
      success: true,
      boloes: boloesComJogos || []
    })
  } catch (error) {
    console.error("Erro ao buscar bolões:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        boloes: []
      },
      { status: 500 }
    )
  }
}
