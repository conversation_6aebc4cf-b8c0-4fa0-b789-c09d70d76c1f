import { initializeDatabase, executeQuery } from "../lib/database-config.js"

async function createAfiliadosTable() {
  try {
    console.log('🏗️ Criando tabela de afiliados...')
    
    await initializeDatabase()
    
    // Criar tabela de afiliados
    console.log('👥 Criando tabela de afiliados...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS afiliados (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        telefone VARCHAR(20),
        codigo_afiliado VARCHAR(20) UNIQUE NOT NULL,
        percentual_comissao DECIMAL(5,2) DEFAULT 5.00,
        cpa_valor DECIMAL(10,2) DEFAULT 0.00,
        tipo_comissao ENUM('percentual', 'cpa') DEFAULT 'percentual',
        comissao_total DECIMAL(10,2) DEFAULT 0.00,
        total_indicacoes INT DEFAULT 0,
        senha_hash VARCHAR(255) NOT NULL,
        status ENUM('ativo', 'inativo', 'bloqueado') DEFAULT 'ativo',
        usuario_id INT NULL,
        data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_codigo_afiliado (codigo_afiliado),
        INDEX idx_status (status),
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela afiliados criada')

    // Adicionar campo afiliado_id na tabela usuarios para rastrear indicações
    console.log('🔗 Adicionando campo afiliado_id na tabela usuarios...')
    try {
      await executeQuery(`
        ALTER TABLE usuarios
        ADD COLUMN afiliado_id INT NULL,
        ADD INDEX idx_afiliado_id (afiliado_id),
        ADD FOREIGN KEY (afiliado_id) REFERENCES afiliados(id) ON DELETE SET NULL
      `)
      console.log('✅ Campo afiliado_id adicionado na tabela usuarios')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('⚠️ Campo afiliado_id já existe na tabela usuarios')
      } else {
        throw error
      }
    }
    console.log('✅ Tabela afiliados criada')

    // Criar tabela de indicações dos afiliados
    console.log('📊 Criando tabela de indicações...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS afiliado_indicacoes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        afiliado_id INT NOT NULL,
        usuario_indicado_id INT NOT NULL,
        valor_comissao DECIMAL(10,2) DEFAULT 0.00,
        status ENUM('pendente', 'pago', 'cancelado') DEFAULT 'pendente',
        data_indicacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_pagamento TIMESTAMP NULL,
        INDEX idx_afiliado (afiliado_id),
        INDEX idx_usuario_indicado (usuario_indicado_id),
        INDEX idx_status (status),
        FOREIGN KEY (afiliado_id) REFERENCES afiliados(id) ON DELETE CASCADE,
        FOREIGN KEY (usuario_indicado_id) REFERENCES usuarios(id) ON DELETE CASCADE,
        UNIQUE KEY unique_indicacao (afiliado_id, usuario_indicado_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela afiliado_indicacoes criada')

    // Verificar se as tabelas foram criadas
    const tables = await executeQuery(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME IN ('afiliados', 'afiliado_indicacoes')
      ORDER BY TABLE_NAME
    `)

    console.log('\n📊 Tabelas de afiliados criadas:')
    tables.forEach(table => {
      console.log(`   ✅ ${table.TABLE_NAME}`)
    })

    console.log('\n🎉 Sistema de afiliados configurado com sucesso!')
    console.log('\n📝 Próximos passos:')
    console.log('  1. Acesse: http://localhost:3000/admin/afiliados')
    console.log('  2. Crie seus primeiros afiliados')
    console.log('  3. Configure as comissões')
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Erro ao criar tabelas de afiliados:', error)
    process.exit(1)
  }
}

createAfiliadosTable()
