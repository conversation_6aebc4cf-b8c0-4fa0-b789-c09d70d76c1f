// Configuração para usar APENAS MySQL
// Conforme solicitado pelo usuário - NUNCA usar SQLite

console.log('🔧 Usando MySQL exclusivamente')
const databaseModule = await import('./database.js')

// Re-exportar todas as funções
export const {
  initializeDatabase,
  getDatabase,
  executeQuery,
  executeQuerySingle,
  getCampeonatos,
  getCampeonatosStats,
  closeDatabase,
  // Funções para usuários
  getUsuarios,
  getUsuariosStats,
  // Funções para cambistas
  getCambistas,
  getCambistasStats,
  createCambista,
  // Funções para afiliados
  getAfiliados,
  getAfiliadosStats,
  createAfiliado,
  updateAfiliado,
  deleteAfiliado,
  // Funções para bolões
  getBoloes,
  getBoloesStats,
  createBolao,
  // Funções para jogos
  getJogos,
  getJogosStats,
  // Funções para dashboard
  getDashboardStats
} = databaseModule
