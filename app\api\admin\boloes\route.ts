import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    // Garantir que o banco está inicializado
    await initializeDatabase()

    // Buscar bolões
    const boloes = await executeQuery(`
      SELECT
        b.*,
        COUNT(DISTINCT a.usuario_id) as participantes,
        COALESCE(SUM(a.valor_total), 0) as faturamento_atual
      FROM boloes b
      LEFT JOIN apostas a ON b.id = a.bolao_id AND a.status = 'paga'
      GROUP BY b.id
      ORDER BY b.data_criacao DESC
    `)

    // Buscar estatísticas
    const statsQueries = await Promise.all([
      executeQuery('SELECT COUNT(*) as count FROM boloes WHERE status = "ativo"'),
      executeQuery('SELECT COUNT(DISTINCT usuario_id) as count FROM apostas'),
      executeQuery('SELECT COALESCE(SUM(valor_total), 0) as total FROM apostas WHERE status = "paga"'),
      executeQuery('SELECT COUNT(*) as count FROM boloes WHERE DATE(data_fim) = CURDATE()')
    ])

    const stats = {
      ativos: statsQueries[0][0]?.count || 0,
      participantes: statsQueries[1][0]?.count || 0,
      faturamento: statsQueries[2][0]?.total || 0,
      finalizandoHoje: statsQueries[3][0]?.count || 0
    }

    return NextResponse.json({
      success: true,
      boloes: boloes || [],
      stats: stats,
    })
  } catch (error) {
    console.error("Erro ao buscar bolões:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error).message || "Erro desconhecido",
        boloes: [],
        stats: { ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 },
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const data = await request.json()

    // Validação dos campos obrigatórios
    if (!data.nome || !data.valor_aposta || !data.premio_total || !data.data_inicio || !data.data_fim) {
      return NextResponse.json(
        {
          success: false,
          error: "Campos obrigatórios: nome, valor_aposta, premio_total, data_inicio, data_fim",
        },
        { status: 400 }
      )
    }

    // Buscar o usuário admin (assumindo que é o primeiro admin)
    const adminUsers = await executeQuery("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    if (!adminUsers || adminUsers.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: "Usuário administrador não encontrado",
        },
        { status: 400 }
      )
    }

    const adminUser = adminUsers[0]

    // Criar bolão
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por, regras,
        campeonatos_selecionados, partidas_selecionadas, data_criacao
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      data.nome,
      data.descricao || '',
      parseFloat(data.valor_aposta),
      parseFloat(data.premio_total),
      data.max_participantes || 100,
      data.min_acertos || 3,
      data.data_inicio,
      data.data_fim,
      data.status || 'ativo',
      adminUser.id,
      data.regras || null,
      JSON.stringify(data.campeonatos_selecionados || []),
      JSON.stringify(data.partidas_selecionadas || [])
    ])

    const bolaoId = result.insertId

    return NextResponse.json({
      success: true,
      id: bolaoId,
      message: "Bolão criado com sucesso",
    })
  } catch (error) {
    console.error("Erro ao criar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}
