import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function createFamousMatches() {
  try {
    console.log('⚽ Criando partidas com times famosos...')
    
    await initializeDatabase()
    
    // Buscar times famosos com logos
    const timesFamosos = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url FROM times 
      WHERE logo_url IS NOT NULL AND logo_url != ''
      AND (
        nome LIKE '%Flamengo%' OR nome LIKE '%Palmeiras%' OR nome LIKE '%São Paulo%' OR
        nome LIKE '%Corinthians%' OR nome LIKE '%Santos%' OR nome LIKE '%Grêmio%' OR
        nome LIKE '%Internacional%' OR nome LIKE '%Atlético%' OR nome LIKE '%Cruzeiro%' OR
        nome LIKE '%Botafogo%' OR nome LIKE '%Vasco%' OR nome LIKE '%Fluminense%' OR
        nome LIKE '%Barcelona%' OR nome LIKE '%Real Madrid%' OR nome LIKE '%Manchester%' OR
        nome LIKE '%Liverpool%' OR nome LIKE '%Chelsea%' OR nome LIKE '%Arsenal%' OR
        nome LIKE '%Bayern%' OR nome LIKE '%Juventus%' OR nome LIKE '%Milan%'
      )
      ORDER BY nome
    `)
    
    console.log(`🌟 Encontrados ${timesFamosos.length} times famosos com logos`)
    
    // Buscar campeonatos
    const campeonatos = await executeQuery(`
      SELECT id, nome FROM campeonatos 
      WHERE status = 'ativo'
      ORDER BY id
      LIMIT 5
    `)
    
    console.log(`🏆 Encontrados ${campeonatos.length} campeonatos`)
    
    // Criar partidas clássicas
    const partidasClassicas = [
      // Clássicos brasileiros
      { casa: 'Flamengo', fora: 'Vasco', campeonato: 'Brasileirão' },
      { casa: 'Corinthians', fora: 'Palmeiras', campeonato: 'Brasileirão' },
      { casa: 'São Paulo', fora: 'Santos', campeonato: 'Brasileirão' },
      { casa: 'Grêmio', fora: 'Internacional', campeonato: 'Copa do Brasil' },
      { casa: 'Atlético Mineiro', fora: 'Cruzeiro', campeonato: 'Copa do Brasil' },
      { casa: 'Botafogo', fora: 'Fluminense', campeonato: 'Copa Libertadores' },
      
      // Clássicos internacionais
      { casa: 'Barcelona', fora: 'Real Madrid', campeonato: 'La Liga' },
      { casa: 'Manchester United', fora: 'Manchester City', campeonato: 'Premier League' },
      { casa: 'Liverpool', fora: 'Chelsea', campeonato: 'Premier League' },
      { casa: 'Arsenal', fora: 'Tottenham', campeonato: 'Premier League' },
      { casa: 'Bayern Munich', fora: 'Borussia Dortmund', campeonato: 'Bundesliga' },
      { casa: 'Juventus', fora: 'AC Milan', campeonato: 'Serie A' },
      { casa: 'Inter Milan', fora: 'AC Milan', campeonato: 'Serie A' }
    ]
    
    let partidasCriadas = 0
    
    for (const classico of partidasClassicas) {
      try {
        // Encontrar time da casa
        const timeCasa = timesFamosos.find(t => 
          t.nome.toLowerCase().includes(classico.casa.toLowerCase()) ||
          t.nome_curto.toLowerCase().includes(classico.casa.toLowerCase())
        )
        
        // Encontrar time de fora
        const timeFora = timesFamosos.find(t => 
          t.nome.toLowerCase().includes(classico.fora.toLowerCase()) ||
          t.nome_curto.toLowerCase().includes(classico.fora.toLowerCase())
        )
        
        // Encontrar campeonato
        const campeonato = campeonatos.find(c => 
          c.nome.toLowerCase().includes(classico.campeonato.toLowerCase())
        ) || campeonatos[0] // Usar primeiro campeonato se não encontrar
        
        if (timeCasa && timeFora && campeonato && timeCasa.id !== timeFora.id) {
          // Data aleatória nos próximos 30 dias
          const dataJogo = new Date()
          dataJogo.setDate(dataJogo.getDate() + Math.floor(Math.random() * 30))
          
          // Inserir partida
          await executeQuery(`
            INSERT INTO jogos (
              campeonato_id, time_casa_id, time_fora_id, data_jogo, 
              rodada, status, data_criacao
            ) VALUES (?, ?, ?, ?, ?, 'agendado', NOW())
          `, [
            campeonato.id,
            timeCasa.id,
            timeFora.id,
            dataJogo.toISOString(),
            Math.floor(Math.random() * 38) + 1
          ])
          
          console.log(`✅ ${timeCasa.nome_curto} vs ${timeFora.nome_curto} - ${campeonato.nome}`)
          partidasCriadas++
        } else {
          console.log(`⚠️ Não foi possível criar: ${classico.casa} vs ${classico.fora}`)
        }
        
      } catch (error) {
        console.error(`❌ Erro ao criar ${classico.casa} vs ${classico.fora}:`, error.message)
      }
    }
    
    console.log(`\n🎉 ${partidasCriadas} partidas clássicas criadas!`)
    
    // Verificar total de partidas
    const [total] = await executeQuery(`
      SELECT COUNT(*) as count FROM jogos WHERE status = 'agendado'
    `)
    
    console.log(`📊 Total de partidas agendadas: ${total.count}`)
    
    // Mostrar algumas partidas com logos
    const partidasComLogos = await executeQuery(`
      SELECT 
        tc.nome_curto as time_casa,
        tc.logo_url as casa_logo,
        tf.nome_curto as time_fora,
        tf.logo_url as fora_logo,
        c.nome as campeonato,
        j.data_jogo
      FROM jogos j
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE tc.logo_url IS NOT NULL AND tf.logo_url IS NOT NULL
      AND j.status = 'agendado'
      ORDER BY j.data_jogo
      LIMIT 10
    `)
    
    console.log(`\n🎨 Exemplos de partidas com escudos:`)
    partidasComLogos.forEach(p => {
      const data = new Date(p.data_jogo).toLocaleDateString('pt-BR')
      console.log(`   ${p.time_casa} vs ${p.time_fora} - ${p.campeonato} (${data})`)
    })
    
  } catch (error) {
    console.error('❌ Erro ao criar partidas famosas:', error.message)
  }
}

// Executar
createFamousMatches()
